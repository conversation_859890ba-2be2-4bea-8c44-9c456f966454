// TTM Squeeze Trading System - Frontend JavaScript

class TTMSqueezeApp {
    constructor() {
        this.init();
        this.setupEventListeners();
        this.startPeriodicUpdates();
    }

    init() {
        console.log('TTM Squeeze Trading System initialized');
        this.updateDashboard();
        this.loadScannerStatus();
    }

    setupEventListeners() {
        // Scanner controls
        document.getElementById('start-scanner').addEventListener('click', () => this.startScanner());
        document.getElementById('stop-scanner').addEventListener('click', () => this.stopScanner());

        // Analysis
        document.getElementById('analyze-btn').addEventListener('click', () => this.analyzeSymbol());
        document.getElementById('analysis-symbol').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.analyzeSymbol();
        });

        // Screener
        document.getElementById('run-screener').addEventListener('click', () => this.runScreener());

        // Trading
        document.getElementById('order-form').addEventListener('submit', (e) => this.placeOrder(e));
        document.getElementById('order-type').addEventListener('change', () => this.togglePriceField());

        // Tab switching
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => this.onTabSwitch(e.target.getAttribute('data-bs-target')));
        });
    }

    startPeriodicUpdates() {
        // Update dashboard every 30 seconds
        setInterval(() => this.updateDashboard(), 30000);
        
        // Update alerts every 10 seconds
        setInterval(() => this.loadAlerts(), 10000);
        
        // Update scanner stats every 15 seconds
        setInterval(() => this.loadScannerStatus(), 15000);
    }

    async updateDashboard() {
        try {
            // Update account info
            const accountResponse = await fetch('/api/trading/account');
            if (accountResponse.ok) {
                const accountData = await accountResponse.json();
                if (accountData.success) {
                    document.getElementById('portfolio-value').textContent = 
                        `$${Number(accountData.data.portfolio_value || 0).toLocaleString()}`;
                }
            }

            // Update positions
            const positionsResponse = await fetch('/api/trading/positions');
            if (positionsResponse.ok) {
                const positionsData = await positionsResponse.json();
                if (positionsData.success) {
                    document.getElementById('position-count').textContent = positionsData.data.length;
                    this.updatePositionsTable(positionsData.data);
                }
            }

        } catch (error) {
            console.error('Error updating dashboard:', error);
        }
    }

    async loadScannerStatus() {
        try {
            const response = await fetch('/api/scanner/status');
            const data = await response.json();
            
            if (data.success) {
                const status = data.data.is_running ? 'Running' : 'Stopped';
                const statusClass = data.data.is_running ? 'status-running' : 'status-stopped';
                
                document.getElementById('scanner-status').textContent = status;
                document.getElementById('scanner-status').className = statusClass;
                
                // Update scanner stats
                const stats = data.data;
                document.getElementById('scanner-stats').innerHTML = `
                    <div>Total Scans: ${stats.total_scans || 0}</div>
                    <div>Universe Size: ${stats.universe_size || 0}</div>
                    <div>Alerts Generated: ${stats.alerts_generated || 0}</div>
                    <div>Avg Scan Time: ${(stats.scan_duration_avg || 0).toFixed(2)}s</div>
                `;
            }
        } catch (error) {
            console.error('Error loading scanner status:', error);
        }
    }

    async startScanner() {
        try {
            const response = await fetch('/api/scanner/start', { method: 'POST' });
            const data = await response.json();
            
            if (data.success) {
                this.showAlert('Scanner started successfully', 'success');
                this.loadScannerStatus();
            } else {
                this.showAlert('Failed to start scanner: ' + data.error, 'danger');
            }
        } catch (error) {
            this.showAlert('Error starting scanner: ' + error.message, 'danger');
        }
    }

    async stopScanner() {
        try {
            const response = await fetch('/api/scanner/stop', { method: 'POST' });
            const data = await response.json();
            
            if (data.success) {
                this.showAlert('Scanner stopped successfully', 'success');
                this.loadScannerStatus();
            } else {
                this.showAlert('Failed to stop scanner: ' + data.error, 'danger');
            }
        } catch (error) {
            this.showAlert('Error stopping scanner: ' + error.message, 'danger');
        }
    }

    async loadAlerts() {
        try {
            const response = await fetch('/api/scanner/alerts?limit=50');
            const data = await response.json();
            
            if (data.success) {
                this.updateAlertsTable(data.data);
                document.getElementById('alert-count').textContent = data.data.length;
            }
        } catch (error) {
            console.error('Error loading alerts:', error);
        }
    }

    updateAlertsTable(alerts) {
        const tbody = document.querySelector('#alerts-table tbody');
        
        if (alerts.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No alerts yet</td></tr>';
            return;
        }

        tbody.innerHTML = alerts.map(alert => {
            const time = this.formatTimeToCSTWithAMPM(alert.timestamp);
            const priorityClass = `alert-priority-${alert.priority}`;
            const strengthBar = this.createStrengthBar(alert.overall_strength);
            const timeframesDisplay = this.createTimeframesDisplay(alert.timeframes_triggered, alert.timeframe_details);

            return `
                <tr class="${priorityClass}">
                    <td>${time}</td>
                    <td><strong>${alert.symbol}</strong></td>
                    <td>${alert.alert_type}</td>
                    <td>${alert.message}</td>
                    <td>${timeframesDisplay}</td>
                    <td>${strengthBar}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="app.analyzeSymbolFromAlert('${alert.symbol}')">
                            <i class="fas fa-chart-line"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    createStrengthBar(strength) {
        const percentage = Math.round(strength * 100);
        const strengthClass = strength > 0.7 ? 'high' : strength > 0.4 ? 'medium' : 'low';

        return `
            <div class="signal-strength signal-strength-${strengthClass}">
                <div class="signal-strength-fill" style="width: ${percentage}%"></div>
            </div>
            <small class="text-muted">${percentage}%</small>
        `;
    }

    createTimeframesDisplay(timeframes, details) {
        if (!timeframes || timeframes.length === 0) {
            return '<span class="text-muted">None</span>';
        }

        // Create badges for each timeframe
        const badges = timeframes.map(tf => {
            const detail = details && details[tf] ? details[tf] : '';
            const badgeClass = tf.includes('15') ? 'badge-primary' :
                              tf.includes('5') ? 'badge-success' :
                              tf.includes('30') ? 'badge-warning' : 'badge-secondary';

            return `<span class="badge ${badgeClass}" title="${detail}">${tf}</span>`;
        }).join(' ');

        return badges;
    }

    async analyzeSymbol() {
        const symbol = document.getElementById('analysis-symbol').value.trim().toUpperCase();
        
        if (!symbol) {
            this.showAlert('Please enter a symbol', 'warning');
            return;
        }

        const resultDiv = document.getElementById('analysis-result');
        resultDiv.innerHTML = '<div class="text-center"><div class="loading-spinner"></div> Analyzing...</div>';

        try {
            const response = await fetch(`/api/analysis/symbol/${symbol}`);
            const data = await response.json();
            
            if (data.success) {
                this.displayAnalysisResult(data.data);
            } else {
                resultDiv.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
            }
        } catch (error) {
            resultDiv.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
        }
    }

    analyzeSymbolFromAlert(symbol) {
        document.getElementById('analysis-symbol').value = symbol;
        
        // Switch to analysis tab
        const analysisTab = document.getElementById('analysis-tab');
        const tab = new bootstrap.Tab(analysisTab);
        tab.show();
        
        this.analyzeSymbol();
    }

    displayAnalysisResult(signal) {
        const resultDiv = document.getElementById('analysis-result');
        
        const setupStatus = signal.is_valid_setup ? 
            '<span class="badge bg-success">Valid Setup</span>' : 
            '<span class="badge bg-secondary">No Setup</span>';
        
        const trendStatus = signal.trend_alignment ? 
            '<span class="badge bg-success">Bullish Trend</span>' : 
            '<span class="badge bg-danger">Bearish Trend</span>';
        
        const volumeStatus = signal.volume_confirmation ? 
            '<span class="badge bg-success">Volume OK</span>' : 
            '<span class="badge bg-warning">Low Volume</span>';

        const strengthBar = this.createStrengthBar(signal.overall_strength);
        
        resultDiv.innerHTML = `
            <div class="analysis-result">
                <h6><strong>${signal.symbol}</strong> - TTM Squeeze Analysis</h6>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>Setup Status:</strong> ${setupStatus}<br>
                        <strong>Recommendation:</strong> <span class="badge bg-primary">${signal.entry_recommendation}</span><br>
                        <strong>Confirmations:</strong> ${signal.confirmations_count}/${signal.required_confirmations}
                    </div>
                    <div class="col-md-6">
                        <strong>Overall Strength:</strong> ${strengthBar}<br>
                        <strong>Trend:</strong> ${trendStatus}<br>
                        <strong>Volume:</strong> ${volumeStatus}
                    </div>
                </div>
                
                <h6>Primary Signal (${signal.primary_signal.timeframe})</h6>
                <div class="mb-3">
                    <span class="signal-indicator ${signal.primary_signal.is_squeeze ? 'squeeze' : 'no-squeeze'}"></span>
                    <strong>Squeeze:</strong> ${signal.primary_signal.is_squeeze ? 'Yes' : 'No'}<br>
                    <strong>Momentum:</strong> <span class="momentum-${signal.primary_signal.momentum_color}">${signal.primary_signal.momentum.toFixed(4)}</span><br>
                    <strong>Entry Signal:</strong> ${signal.primary_signal.entry_signal ? 'Yes' : 'No'}
                </div>
                
                <h6>Confirmation Signals</h6>
                <div class="row">
                    ${Object.entries(signal.confirmation_signals).map(([tf, sig]) => `
                        <div class="col-md-4">
                            <span class="timeframe-badge badge bg-secondary">${tf}</span><br>
                            <small>
                                Squeeze: ${sig.is_squeeze ? 'Yes' : 'No'}<br>
                                Momentum: <span class="momentum-${sig.momentum_color}">${sig.momentum.toFixed(4)}</span>
                            </small>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    async runScreener() {
        const button = document.getElementById('run-screener');
        const originalText = button.innerHTML;
        button.innerHTML = '<div class="loading-spinner"></div> Running...';
        button.disabled = true;

        try {
            const criteria = {
                min_price: parseFloat(document.getElementById('min-price').value),
                max_price: parseFloat(document.getElementById('max-price').value),
                min_volume: parseInt(document.getElementById('min-volume').value),
                ema_alignment: document.getElementById('ema-alignment').checked,
                max_stocks: 50
            };

            const response = await fetch('/api/screener/run', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(criteria)
            });

            const data = await response.json();
            
            if (data.success) {
                this.updateScreenerResults(data.data);
                this.showAlert(`Found ${data.count} stocks meeting criteria`, 'success');
            } else {
                this.showAlert('Screener failed: ' + data.error, 'danger');
            }
        } catch (error) {
            this.showAlert('Error running screener: ' + error.message, 'danger');
        } finally {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    updateScreenerResults(results) {
        const tbody = document.querySelector('#screener-results tbody');
        
        if (results.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">No results found</td></tr>';
            return;
        }

        tbody.innerHTML = results.map(stock => `
            <tr>
                <td><strong>${stock.symbol}</strong></td>
                <td>${stock.company_name}</td>
                <td>${stock.sector}</td>
                <td>$${stock.price.toFixed(2)}</td>
                <td>${stock.volume.toLocaleString()}</td>
                <td>${stock.rsi.toFixed(1)}</td>
                <td>${stock.score.toFixed(1)}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="app.analyzeSymbolFromAlert('${stock.symbol}')">
                        <i class="fas fa-chart-line"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    togglePriceField() {
        const orderType = document.getElementById('order-type').value;
        const priceField = document.getElementById('price-field');
        
        if (orderType === 'limit' || orderType === 'stop_limit') {
            priceField.style.display = 'block';
            document.getElementById('order-price').required = true;
        } else {
            priceField.style.display = 'none';
            document.getElementById('order-price').required = false;
        }
    }

    async placeOrder(event) {
        event.preventDefault();
        
        const orderData = {
            symbol: document.getElementById('order-symbol').value.toUpperCase(),
            side: document.getElementById('order-side').value,
            quantity: parseInt(document.getElementById('order-quantity').value),
            order_type: document.getElementById('order-type').value,
            price: document.getElementById('order-price').value || null
        };

        try {
            const response = await fetch('/api/trading/place_order', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(orderData)
            });

            const data = await response.json();
            
            if (data.success) {
                this.showAlert('Order placed successfully', 'success');
                document.getElementById('order-form').reset();
                this.updateDashboard();
            } else {
                this.showAlert('Order failed: ' + data.error, 'danger');
            }
        } catch (error) {
            this.showAlert('Error placing order: ' + error.message, 'danger');
        }
    }

    updatePositionsTable(positions) {
        const tbody = document.querySelector('#positions-table tbody');

        if (positions.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No positions</td></tr>';
            return;
        }

        tbody.innerHTML = positions.map(pos => {
            const pnlClass = pos.unrealized_pl >= 0 ? 'pnl-positive' : 'pnl-negative';
            const pnlSign = pos.unrealized_pl >= 0 ? '+' : '';

            // Handle fractional shares display
            const quantityDisplay = Number.isInteger(pos.quantity) ?
                pos.quantity.toString() :
                pos.quantity.toFixed(6).replace(/\.?0+$/, '');

            return `
                <tr>
                    <td><strong>${pos.symbol}</strong></td>
                    <td>${quantityDisplay}</td>
                    <td>$${pos.avg_entry_price.toFixed(2)}</td>
                    <td>$${pos.current_price.toFixed(2)}</td>
                    <td class="${pnlClass}">${pnlSign}$${pos.unrealized_pl.toFixed(2)}</td>
                    <td>
                        <button class="btn btn-sm btn-danger" onclick="app.closePosition('${pos.symbol}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    async closePosition(symbol) {
        if (!confirm(`Are you sure you want to close position in ${symbol}?`)) {
            return;
        }

        try {
            const response = await fetch(`/api/trading/close_position/${symbol}`, {
                method: 'POST'
            });

            const data = await response.json();
            
            if (data.success) {
                this.showAlert(`Position in ${symbol} closed successfully`, 'success');
                this.updateDashboard();
            } else {
                this.showAlert(`Failed to close position: ${data.error}`, 'danger');
            }
        } catch (error) {
            this.showAlert(`Error closing position: ${error.message}`, 'danger');
        }
    }

    onTabSwitch(target) {
        switch (target) {
            case '#trading':
                this.updateDashboard();
                break;
            case '#risk':
                this.loadRiskAssessment();
                break;
        }
    }

    async loadRiskAssessment() {
        try {
            const response = await fetch('/api/risk/portfolio');
            const data = await response.json();
            
            if (data.success) {
                this.displayRiskAssessment(data.data);
            }
        } catch (error) {
            console.error('Error loading risk assessment:', error);
        }
    }

    displayRiskAssessment(risk) {
        const riskDiv = document.getElementById('portfolio-risk');
        const riskClass = `risk-${risk.risk_level}`;
        
        riskDiv.innerHTML = `
            <div class="mb-3">
                <h6>Portfolio Overview</h6>
                <div>Total Value: <strong>$${risk.total_value.toLocaleString()}</strong></div>
                <div>Total Risk: <strong>$${risk.total_risk.toLocaleString()}</strong></div>
                <div>Risk Level: <span class="${riskClass}">${risk.risk_level.toUpperCase()}</span></div>
            </div>
            
            <div class="mb-3">
                <h6>Risk Metrics</h6>
                <div>Risk Percentage: <strong>${(risk.risk_percentage * 100).toFixed(1)}%</strong> / ${(risk.max_risk_percentage * 100).toFixed(1)}%</div>
                <div>Positions: <strong>${risk.position_count}</strong> / ${risk.max_positions}</div>
                <div>Largest Position: <strong>${(risk.largest_position_pct * 100).toFixed(1)}%</strong></div>
            </div>
            
            ${risk.recommendations.length > 0 ? `
                <div>
                    <h6>Recommendations</h6>
                    <ul class="list-unstyled">
                        ${risk.recommendations.map(rec => `<li><i class="fas fa-exclamation-triangle text-warning"></i> ${rec}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}
        `;
    }

    showAlert(message, type = 'info') {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new TTMSqueezeApp();
});
