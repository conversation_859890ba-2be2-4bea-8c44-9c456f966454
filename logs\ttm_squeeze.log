2025-06-25 08:35:43 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:35:44 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:44 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:35:45 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:35:45 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:35:45 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:45 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:35:45 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:35:45 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:35:45 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:35:45 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:45 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:35:45 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:35:45 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:35:45 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:35:45 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:35:45 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:45 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:35:46 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:35:46 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:35:46 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:35:46 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:35:46 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-25 08:35:46 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 08:35:46 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:35:47 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:35:47 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:47 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:35:47 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:35:47 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:35:47 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:47 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:35:47 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:35:47 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:35:47 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:35:47 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:47 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:35:47 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:35:47 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:35:47 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:35:47 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:35:48 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:48 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:35:48 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:35:48 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:35:48 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:35:48 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:35:48 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:35:48 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET / HTTP/1.1" 200 -
2025-06-25 08:36:09 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\unicode_escape.py', reloading
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-25 08:36:09 - src.trading.alpaca_trader - ERROR - Error getting positions: invalid literal for int() with base 10: '0.0009975'
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:36:09 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:36:10 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:36:10 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:36:10 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:36:10 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:36:10 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:36:11 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:36:11 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:36:11 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:36:11 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:36:11 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:36:11 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:36:11 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:36:11 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:36:11 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:36:11 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:36:11 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:36:11 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:36:11 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:36:11 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:36:11 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:36:11 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:36:11 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:36:11 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:36:11 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:36:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:36:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:36:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:36:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:36:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:36:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:36:39 - src.trading.alpaca_trader - ERROR - Error getting positions: invalid literal for int() with base 10: '0.0009975'
2025-06-25 08:36:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:36:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:36:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:36:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:37:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:37:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:37:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:37:09 - src.trading.alpaca_trader - ERROR - Error getting positions: invalid literal for int() with base 10: '0.0009975'
2025-06-25 08:37:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:37:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:37:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:37:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:37:36 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 08:37:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:36] "POST /api/scanner/start HTTP/1.1" 200 -
2025-06-25 08:37:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:36] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:37:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:37:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:37:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:37:39 - src.trading.alpaca_trader - ERROR - Error getting positions: invalid literal for int() with base 10: '0.0009975'
2025-06-25 08:37:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:37:45 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\cli_test.py', reloading
2025-06-25 08:37:45 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\cli_test.py', reloading
2025-06-25 08:37:46 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:37:46 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:37:47 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:37:47 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:37:47 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:37:47 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:37:47 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:37:47 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:37:47 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:37:47 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:37:47 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:37:47 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:37:47 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:37:47 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:37:47 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:37:47 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:37:47 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:37:48 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:37:48 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:37:48 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:37:48 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:37:48 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:37:48 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:37:48 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:37:48 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:37:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:37:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:37:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:38:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:38:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:38:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:38:09 - src.trading.alpaca_trader - ERROR - Error getting positions: invalid literal for int() with base 10: '0.0009975'
2025-06-25 08:38:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:38:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:38:21 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 08:38:21 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:21] "POST /api/scanner/start HTTP/1.1" 200 -
2025-06-25 08:38:21 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:21] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:38:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:38:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:38:29 - src.scanner.stock_screener - INFO - Refreshing stock universe...
2025-06-25 08:38:29 - src.data.fmp_provider - INFO - Retrieved 503 S&P 500 symbols
2025-06-25 08:38:29 - src.data.data_manager - INFO - Added 503 S&P 500 stocks to universe
2025-06-25 08:38:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:38:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:38:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:38:39 - src.trading.alpaca_trader - ERROR - Error getting positions: invalid literal for int() with base 10: '0.0009975'
2025-06-25 08:38:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:38:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:38:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:38:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:39:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:39:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:39:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:39:09 - src.trading.alpaca_trader - ERROR - Error getting positions: invalid literal for int() with base 10: '0.0009975'
2025-06-25 08:39:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:39:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:39:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:39:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:39:35 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\alpaca_trader.py', reloading
2025-06-25 08:39:35 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\alpaca_trader.py', reloading
2025-06-25 08:39:35 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:39:36 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:39:36 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:36 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:39:36 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:39:36 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:39:36 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:36 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:39:37 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:39:37 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:39:37 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:39:37 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:37 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:39:37 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:39:37 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:39:37 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:39:37 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:39:37 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:37 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:39:37 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:39:37 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:39:37 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:39:37 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:39:37 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:39:37 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:39:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:39:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:39:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:39:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:39:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:39:51 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\alpaca_trader.py', reloading
2025-06-25 08:39:51 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\alpaca_trader.py', reloading
2025-06-25 08:39:51 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:39:52 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:39:52 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:52 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:39:53 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:39:53 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:39:53 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:53 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:39:53 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:39:53 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:39:53 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:39:53 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:53 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:39:53 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:39:53 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:39:53 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:39:53 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:39:53 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:53 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:39:53 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:39:53 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:39:53 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:39:53 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:39:53 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:39:53 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:39:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:39:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:40:06 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\alpaca_trader.py', reloading
2025-06-25 08:40:06 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\alpaca_trader.py', reloading
2025-06-25 08:40:07 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:40:08 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:40:08 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:08 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:40:08 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:40:08 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:40:08 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:08 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:40:08 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:40:08 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:40:08 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:40:08 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:08 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:40:08 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:40:08 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:40:08 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:40:08 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:40:09 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:09 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:40:09 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:40:09 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:40:09 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:40:09 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:40:09 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:40:09 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:40:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:40:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:40:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:40:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:40:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:40:19 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\risk_manager.py', reloading
2025-06-25 08:40:19 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\risk_manager.py', reloading
2025-06-25 08:40:20 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:40:21 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:40:21 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:21 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:40:21 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:40:21 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:40:21 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:21 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:40:22 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:40:22 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:40:22 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:40:22 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:22 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:40:22 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:40:22 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:40:22 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:40:22 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:40:22 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:22 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:40:22 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:40:22 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:40:22 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:40:22 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:40:22 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:40:22 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:40:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:40:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:40:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:40:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:40:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:40:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:40:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:40:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:40:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:41:04 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\test_fractional_fix.py', reloading
2025-06-25 08:41:04 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\test_fractional_fix.py', reloading
2025-06-25 08:41:05 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:41:06 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:41:08 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:41:08 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:41:08 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:41:08 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:41:08 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:41:08 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:41:08 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:41:08 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:41:08 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:41:08 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:41:08 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:41:08 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:41:08 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:41:08 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:41:08 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:41:09 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:41:09 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:41:09 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:41:09 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:41:09 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:41:09 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:41:09 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:41:09 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:41:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:41:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:41:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:41:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:41:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:41:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:41:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:41:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:41:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:41:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:41:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:41:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:41:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:41:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:42:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:42:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:42:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:42:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:42:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:42:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:42:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:42:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:42:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:42:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:42:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:42:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:42:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:42:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:43:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:43:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:43:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:43:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:43:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:43:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:43:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:43:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:43:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:43:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:43:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:43:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:43:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:43:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:44:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:44:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:44:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:44:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:44:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:44:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:44:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:44:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:44:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:44:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:44:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:44:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:44:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:44:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:45:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:45:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:45:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:45:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:45:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:45:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:45:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:45:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:45:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:45:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:45:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:45:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:45:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:45:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:46:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:46:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:46:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:46:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:46:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:46:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:46:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:46:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:46:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:46:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:46:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:46:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:46:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:46:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:47:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:47:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:47:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:47:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:47:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:47:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:47:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:47:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:47:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:47:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:47:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:47:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:47:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:47:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:48:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:48:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:48:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:48:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:48:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:48:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:48:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:48:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:48:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:48:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:48:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:48:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:48:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:48:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:49:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:49:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:49:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:49:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:49:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:49:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:49:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:49:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:49:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:49:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:49:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:49:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:49:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:49:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:50:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:50:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:50:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:50:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:50:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:50:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:50:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:50:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:50:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:50:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:50:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:50:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:50:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:50:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:51:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:51:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:51:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:51:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:51:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:51:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:51:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:51:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:51:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:51:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:51:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:51:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:51:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:51:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:52:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:52:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:52:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:52:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:52:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:52:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:52:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:52:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:52:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:52:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:52:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:52:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:52:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:52:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:53:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:53:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:53:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:53:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:53:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:53:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:53:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:53:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:53:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:53:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:53:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:53:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:53:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:53:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:54:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:54:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:54:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:54:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:54:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:54:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:54:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:54:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:54:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:54:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:54:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:54:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:54:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:54:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:55:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:55:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:55:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:55:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:55:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:55:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:55:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:55:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:55:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:55:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:55:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:55:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:55:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:55:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:56:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:56:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:56:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:56:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:56:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:56:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:56:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:56:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:56:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:56:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:56:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:56:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:56:53 - src.scanner.stock_screener - INFO - Refreshing stock universe...
2025-06-25 08:56:53 - src.data.fmp_provider - INFO - Retrieved 503 S&P 500 symbols
2025-06-25 08:56:53 - src.data.data_manager - INFO - Added 503 S&P 500 stocks to universe
2025-06-25 08:56:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:56:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:57:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:57:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:57:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:57:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:57:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:57:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:57:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:57:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:57:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:57:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:57:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:57:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:02:16 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:02:16 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:02:16 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:02:16 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:02:16 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-24T08:02:16.760799' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T08:02:16.760799": extra text: "T08:02:16.760799"
2025-06-25 09:02:17 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-25T00:42:17.345972' as RFC3339 or 2006-01-02 time: parsing time "2025-06-25T00:42:17.345972": extra text: "T00:42:17.345972"
2025-06-25 09:02:17 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-23T07:02:17.471312' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:17.471312": extra text: "T07:02:17.471312"
2025-06-25 09:02:17 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-24T08:02:17.619130' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T08:02:17.619130": extra text: "T08:02:17.619130"
2025-06-25 09:02:17 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-25T00:42:17.747286' as RFC3339 or 2006-01-02 time: parsing time "2025-06-25T00:42:17.747286": extra text: "T00:42:17.747286"
2025-06-25 09:02:17 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-23T07:02:17.871360' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:17.871360": extra text: "T07:02:17.871360"
2025-06-25 09:02:18 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-24T08:02:18.023134' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T08:02:18.023134": extra text: "T08:02:18.023134"
2025-06-25 09:02:18 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-25T00:42:18.443080' as RFC3339 or 2006-01-02 time: parsing time "2025-06-25T00:42:18.443080": extra text: "T00:42:18.443080"
2025-06-25 09:02:18 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-23T07:02:18.556751' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:18.556751": extra text: "T07:02:18.556751"
2025-06-25 09:02:18 - src.data.alpaca_provider - ERROR - Error getting historical data for SPY: Invalid format for parameter start: error parsing '2025-06-24T08:02:18.902692' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T08:02:18.902692": extra text: "T08:02:18.902692"
2025-06-25 09:02:19 - src.data.alpaca_provider - ERROR - Error getting historical data for SPY: Invalid format for parameter start: error parsing '2025-06-25T00:42:19.015290' as RFC3339 or 2006-01-02 time: parsing time "2025-06-25T00:42:19.015290": extra text: "T00:42:19.015290"
2025-06-25 09:02:19 - src.data.alpaca_provider - ERROR - Error getting historical data for SPY: Invalid format for parameter start: error parsing '2025-06-23T07:02:19.241807' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:19.241807": extra text: "T07:02:19.241807"
2025-06-25 09:02:19 - src.data.alpaca_provider - ERROR - Error getting historical data for QQQ: Invalid format for parameter start: error parsing '2025-06-24T08:02:19.443166' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T08:02:19.443166": extra text: "T08:02:19.443166"
2025-06-25 09:02:19 - src.data.alpaca_provider - ERROR - Error getting historical data for QQQ: Invalid format for parameter start: error parsing '2025-06-25T00:42:19.556823' as RFC3339 or 2006-01-02 time: parsing time "2025-06-25T00:42:19.556823": extra text: "T00:42:19.556823"
2025-06-25 09:02:19 - src.data.alpaca_provider - ERROR - Error getting historical data for QQQ: Invalid format for parameter start: error parsing '2025-06-23T07:02:19.823436' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:19.823436": extra text: "T07:02:19.823436"
2025-06-25 09:02:20 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:02:20 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:02:20 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:02:20 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:02:20 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-23T07:02:20.545811' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:20.545811": extra text: "T07:02:20.545811"
2025-06-25 09:02:20 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-24T16:22:20.836117' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T16:22:20.836117": extra text: "T16:22:20.836117"
2025-06-25 09:02:21 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-21T05:02:21.111701' as RFC3339 or 2006-01-02 time: parsing time "2025-06-21T05:02:21.111701": extra text: "T05:02:21.111701"
2025-06-25 09:02:21 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-23T07:02:21.270553' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:21.270553": extra text: "T07:02:21.270553"
2025-06-25 09:02:21 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-24T16:22:21.707704' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T16:22:21.707704": extra text: "T16:22:21.707704"
2025-06-25 09:02:21 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-21T05:02:21.837304' as RFC3339 or 2006-01-02 time: parsing time "2025-06-21T05:02:21.837304": extra text: "T05:02:21.837304"
2025-06-25 09:02:22 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-23T07:02:21.985817' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:21.985817": extra text: "T07:02:21.985817"
2025-06-25 09:02:22 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-24T16:22:22.123730' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T16:22:22.123730": extra text: "T16:22:22.123730"
2025-06-25 09:02:22 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-21T05:02:22.249935' as RFC3339 or 2006-01-02 time: parsing time "2025-06-21T05:02:22.249935": extra text: "T05:02:22.249935"
2025-06-25 09:02:22 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:02:22 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:02:22 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:02:22 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:02:22 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:02:22 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-23T07:02:22.809924' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:22.809924": extra text: "T07:02:22.809924"
2025-06-25 09:02:23 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-24T16:22:23.095599' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T16:22:23.095599": extra text: "T16:22:23.095599"
2025-06-25 09:02:24 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-21T05:02:24.250590' as RFC3339 or 2006-01-02 time: parsing time "2025-06-21T05:02:24.250590": extra text: "T05:02:24.250590"
2025-06-25 09:02:24 - src.scanner.real_time_scanner - INFO - Manual scan result for AAPL: avoid
2025-06-25 09:02:24 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-23T07:02:24.409218' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:24.409218": extra text: "T07:02:24.409218"
2025-06-25 09:02:24 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-24T16:22:24.542535' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T16:22:24.542535": extra text: "T16:22:24.542535"
2025-06-25 09:02:24 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-21T05:02:24.671077' as RFC3339 or 2006-01-02 time: parsing time "2025-06-21T05:02:24.671077": extra text: "T05:02:24.671077"
2025-06-25 09:02:24 - src.scanner.real_time_scanner - INFO - Manual scan result for MSFT: hold
2025-06-25 09:02:24 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-23T07:02:24.820633' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:24.820633": extra text: "T07:02:24.820633"
2025-06-25 09:02:25 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-24T16:22:24.959097' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T16:22:24.959097": extra text: "T16:22:24.959097"
2025-06-25 09:02:25 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-21T05:02:25.084204' as RFC3339 or 2006-01-02 time: parsing time "2025-06-21T05:02:25.084204": extra text: "T05:02:25.084204"
2025-06-25 09:02:26 - src.scanner.real_time_scanner - INFO - Manual scan result for TSLA: avoid
2025-06-25 09:02:26 - src.data.alpaca_provider - ERROR - Error getting historical data for SPY: Invalid format for parameter start: error parsing '2025-06-23T07:02:26.434174' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:26.434174": extra text: "T07:02:26.434174"
2025-06-25 09:02:26 - src.data.alpaca_provider - ERROR - Error getting historical data for SPY: Invalid format for parameter start: error parsing '2025-06-24T16:22:26.547501' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T16:22:26.547501": extra text: "T16:22:26.547501"
2025-06-25 09:02:26 - src.data.alpaca_provider - ERROR - Error getting historical data for SPY: Invalid format for parameter start: error parsing '2025-06-21T05:02:26.678284' as RFC3339 or 2006-01-02 time: parsing time "2025-06-21T05:02:26.678284": extra text: "T05:02:26.678284"
2025-06-25 09:02:26 - src.scanner.real_time_scanner - INFO - Manual scan result for SPY: hold
2025-06-25 09:02:26 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:02:26 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:02:27 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:02:27 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:02:27 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:02:27 - src.scanner.real_time_scanner - INFO - Refreshing scan universe...
2025-06-25 09:02:27 - src.scanner.stock_screener - INFO - Refreshing stock universe...
2025-06-25 09:02:28 - src.data.fmp_provider - INFO - Retrieved 503 S&P 500 symbols
2025-06-25 09:02:28 - src.data.data_manager - INFO - Added 503 S&P 500 stocks to universe
2025-06-25 09:03:29 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:03:29 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:29 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:03:29 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:03:29 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:03:29 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:29 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:03:29 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:03:29 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:03:29 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:03:30 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:30 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:03:30 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:03:30 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:03:30 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:03:30 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:03:30 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:30 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:03:30 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:03:30 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:03:30 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:03:30 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:03:30 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:03:30 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:03:30 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-25 09:03:30 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 09:03:30 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:03:31 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:03:31 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:31 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:03:31 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:03:31 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:03:31 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:31 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:03:31 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:03:31 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:03:31 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:03:32 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:32 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:03:32 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:03:32 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:03:32 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:03:32 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:03:32 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:32 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:03:32 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:03:32 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:03:32 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:03:32 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:03:32 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:03:32 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:03:32 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:03:32 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:05:19 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\data\\alpaca_provider.py', reloading
2025-06-25 09:05:19 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\data\\alpaca_provider.py', reloading
2025-06-25 09:05:20 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:05:21 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:05:21 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:21 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:21 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:21 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:21 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:21 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:21 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:21 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:21 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:05:22 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:22 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:05:22 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:05:22 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:05:22 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:05:22 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:05:22 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:22 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:22 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:22 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:22 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:05:22 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:05:22 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:05:22 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:05:22 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:05:22 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:05:29 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\data\\alpaca_provider.py', reloading
2025-06-25 09:05:29 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\data\\alpaca_provider.py', reloading
2025-06-25 09:05:29 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:05:30 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:05:30 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:30 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:30 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:30 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:31 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:31 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:31 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:31 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:31 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:05:31 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:31 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:05:31 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:05:31 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:05:31 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:05:31 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:05:31 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:31 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:31 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:31 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:31 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:05:31 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:05:31 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:05:31 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:05:31 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:05:31 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:05:41 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\data\\alpaca_provider.py', reloading
2025-06-25 09:05:41 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\data\\alpaca_provider.py', reloading
2025-06-25 09:05:42 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:05:42 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:05:43 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:43 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:43 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:43 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:43 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:43 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:43 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:43 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:43 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:05:43 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:43 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:05:43 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:05:43 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:05:43 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:05:43 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:05:43 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:43 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:44 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:44 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:44 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:05:44 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:05:44 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:05:44 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:05:44 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:05:44 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:05:58 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:05:58 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:58 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:59 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:59 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:59 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:59 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:59 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:59 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:59 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:05:59 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:59 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:05:59 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:05:59 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:05:59 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:05:59 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:05:59 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:59 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:06:00 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:06:00 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:06:00 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:06:00 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:06:00 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:06:00 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:06:00 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-25 09:06:00 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 09:06:00 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:06:00 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:06:01 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:06:01 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:06:01 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:06:01 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:06:01 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:06:01 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:06:01 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:06:01 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:06:01 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:06:01 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:06:01 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:06:01 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:06:01 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:06:01 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:06:01 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:06:02 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:06:02 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:06:02 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:06:02 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:06:02 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:06:02 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:06:02 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:06:02 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:06:02 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:06:02 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:08:20 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\test_data_fix.py', reloading
2025-06-25 09:08:20 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\test_data_fix.py', reloading
2025-06-25 09:08:20 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:08:21 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:08:26 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:08:26 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:08:26 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:08:26 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:08:26 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:08:26 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:08:26 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:08:26 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:08:26 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:08:27 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:08:27 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:08:27 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:08:27 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:08:27 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:08:27 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:08:27 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:08:27 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:08:27 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:08:27 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:08:27 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:08:27 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:08:27 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:08:27 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:08:27 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:08:27 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:08:28 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:08:28 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:08:29 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:08:29 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:08:29 - src.data.alpaca_provider - WARNING - Insufficient data for AAPL: got 13, requested 50
2025-06-25 09:08:29 - src.data.alpaca_provider - WARNING - Insufficient data for MSFT: got 7, requested 50
2025-06-25 09:08:29 - src.data.alpaca_provider - WARNING - Insufficient data for TSLA: got 14, requested 50
2025-06-25 09:08:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:36] "GET / HTTP/1.1" 200 -
2025-06-25 09:08:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:36] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-25 09:08:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-25 09:08:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:36] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:08:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:36] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:08:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:37] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:08:46 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:46] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:08:51 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:51] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:08:56 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:56] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:09:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:06] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:09:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:06] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:09:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:06] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:09:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:07] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:09:16 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:16] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:09:21 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:21] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:09:26 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:26] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:09:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:36] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:09:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:36] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:09:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:36] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:09:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:37] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:09:46 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:46] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:09:51 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:51] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:09:56 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:56] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:10:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:06] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:10:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:06] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:10:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:06] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:10:07 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:10:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:07] "POST /api/scanner/start HTTP/1.1" 200 -
2025-06-25 09:10:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:10:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:07] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:10:16 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:16] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:10:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:22] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:10:27 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:27] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:10:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:37] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:10:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:10:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:37] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:10:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:37] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:10:47 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:47] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:10:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:52] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:10:57 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:57] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:11:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:07] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:11:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:11:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:07] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:11:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:07] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:11:17 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:17] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:11:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:37] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:11:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:37] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:11:38 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:38] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:11:38 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:38] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:12:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:12:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:12:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:12:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:12:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:12:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:12:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:12:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:13:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:13:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:13:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:13:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:13:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:13:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:13:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:13:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:14:27 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:27] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:14:27 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:27] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:14:27 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:27] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:14:27 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:27] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:14:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:37] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:14:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:14:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:37] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:14:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:37] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:14:47 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:47] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:14:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:52] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:14:57 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:57] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:15:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:07] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:15:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:15:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:07] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:15:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:07] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:15:17 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:17] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:15:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:22] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:15:27 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:27] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:15:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:15:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:15:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:15:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:16:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:16:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:16:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:16:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:16:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:16:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:16:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:16:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:17:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:17:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:17:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:17:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:17:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:17:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:17:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:17:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:18:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:18:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:18:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:18:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:18:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:18:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:18:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:18:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:19:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:19:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:19:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:19:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:19:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:19:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:19:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:19:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:20:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:20:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:20:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:20:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:20:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:20:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:20:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:20:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:21:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:21:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:21:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:21:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:21:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:21:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:21:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:21:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:22:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:22:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:22:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:22:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:22:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:22:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:22:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:22:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:23:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:23:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:23:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:23:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:23:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:23:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:23:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:23:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:24:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:24:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:24:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:24:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:24:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:24:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:24:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:24:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:25:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:25:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:25:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:25:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:25:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:25:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:25:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:25:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:26:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:26:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:26:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:26:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:26:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:26:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:26:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:26:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:27:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:27:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:27:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:27:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:27:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:27:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:27:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:27:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:28:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:28:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:28:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:28:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:28:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:28:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:28:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:28:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:29:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:29:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:29:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:29:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:29:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:29:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:29:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:29:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:30:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:30:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:30:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:30:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:30:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:30:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:30:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:30:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:31:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:31:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:31:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:31:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:31:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:31:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:31:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:31:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:32:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:32:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:32:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:32:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:32:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:32:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:32:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:32:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:33:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:33:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:33:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:33:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:33:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:33:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:33:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:33:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:34:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:34:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:34:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:34:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:34:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:34:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:34:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:34:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:35:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:35:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:35:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:35:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:35:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:35:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:35:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:35:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:36:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:36:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:36:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:36:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:36:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:36:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:36:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:36:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:37:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:37:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:37:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:37:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:37:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:37:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:37:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:37:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:38:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:38:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:38:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:38:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:38:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:38:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:38:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:38:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:39:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:39:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:39:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:39:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:39:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:39:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:39:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:39:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:40:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:40:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:40:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:40:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:40:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:40:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:40:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:40:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:41:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:41:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:41:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:41:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:41:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:41:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:41:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:41:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:42:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:42:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:42:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:42:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:42:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:42:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:42:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:42:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:43:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:43:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:43:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:43:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:43:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:43:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:43:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:43:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:44:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:44:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:44:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:44:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:44:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:44:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:44:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:44:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:45:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:45:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:45:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:45:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:45:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:45:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:45:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:45:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:46:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:46:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:46:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:46:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:46:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:46:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:46:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:46:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:47:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:47:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:47:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:47:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:47:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:47:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:47:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:47:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:48:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:48:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:48:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:48:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:48:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:48:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:48:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:48:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:49:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:49:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:49:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:49:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:49:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:49:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:49:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:49:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:49:51 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:49:51] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:49:51 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:49:51] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:49:57 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:49:57] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:50:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:50:06] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:50:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:50:06] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:50:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:50:06] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:50:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:50:07] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:50:16 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:50:16] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:50:21 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:50:21] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:50:26 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:50:26] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:50:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:50:36] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:50:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:50:36] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:50:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:50:36] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:50:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:50:37] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:50:46 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:50:46] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:50:51 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:50:51] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:50:56 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:50:56] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:51:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:51:06] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:51:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:51:06] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:51:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:51:06] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:51:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:51:07] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:51:16 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:51:16] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:51:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:51:22] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:51:27 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:51:27] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:51:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:51:36] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:51:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:51:36] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:51:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:51:36] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:51:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:51:37] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:51:52 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\scanner\\real_time_scanner.py', reloading
2025-06-25 09:51:52 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\scanner\\real_time_scanner.py', reloading
2025-06-25 09:51:52 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\scanner\\real_time_scanner.py', reloading
2025-06-25 09:51:52 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\indicators\\multi_timeframe.py', reloading
2025-06-25 09:51:52 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\scanner\\stock_screener.py', reloading
2025-06-25 09:51:52 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\indicators\\ttm_squeeze.py', reloading
2025-06-25 09:51:52 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:51:53 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:51:53 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:51:53 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:51:53 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:51:53 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:51:54 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:51:54 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:51:54 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:51:54 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:51:54 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:51:54 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:51:54 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:51:54 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:51:54 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:51:54 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:51:54 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:51:54 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:51:54 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:51:54 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:51:54 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:51:54 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:51:54 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:51:54 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:51:54 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:51:54 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:51:54 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:52:56 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\scanner\\real_time_scanner.py', reloading
2025-06-25 09:52:56 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\scanner\\real_time_scanner.py', reloading
2025-06-25 09:52:56 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\scanner\\real_time_scanner.py', reloading
2025-06-25 09:52:56 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:52:57 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:52:57 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:52:57 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:52:58 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:52:58 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:52:58 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:52:58 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:52:58 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:52:58 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:52:58 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:52:58 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:52:58 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:52:58 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:52:58 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:52:58 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:52:58 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:52:58 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:52:58 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:52:59 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:52:59 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:52:59 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:52:59 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:52:59 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:52:59 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:52:59 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:52:59 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:53:25 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:53:26 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:53:26 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:53:26 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:53:26 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:53:26 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:53:26 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:53:26 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:53:26 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:53:26 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:53:26 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:53:26 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:53:26 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:53:26 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:53:26 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:53:26 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:53:26 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:53:26 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:53:27 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:53:27 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:53:27 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:53:27 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:53:27 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:53:27 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:53:27 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-25 09:53:27 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 09:53:27 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:53:27 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:53:28 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:53:28 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:53:28 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:53:28 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:53:28 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:53:28 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:53:28 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:53:28 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:53:28 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:53:28 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:53:28 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:53:28 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:53:28 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:53:28 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:53:28 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:53:29 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:53:29 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:53:29 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:53:29 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:53:29 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:53:29 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:53:29 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:53:29 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:53:29 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:53:29 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:53:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:53:39] "GET / HTTP/1.1" 200 -
2025-06-25 09:53:39 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\unicode_escape.py', reloading
2025-06-25 09:53:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:53:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-25 09:53:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:53:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-25 09:53:39 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:53:40 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:53:40 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:53:40 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:53:40 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:53:40 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:53:40 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:53:40 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:53:41 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:53:41 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:53:41 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:53:41 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:53:41 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:53:41 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:53:41 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:53:41 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:53:41 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:53:41 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:53:41 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:53:41 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:53:41 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:53:41 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:53:41 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:53:41 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:53:41 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:53:41 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:53:41 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:53:41 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:53:41] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:53:41 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:53:41] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:53:41 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:53:41] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:53:42 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:53:42 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:53:42] "POST /api/scanner/start HTTP/1.1" 200 -
2025-06-25 09:53:42 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:53:42] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:53:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:53:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:53:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:53:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:53:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:53:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:54:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:54:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:54:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:54:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:54:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:54:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:54:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:54:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:54:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:54:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:54:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:54:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:54:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:54:55 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:55] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:54:55 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:55] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:54:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:54:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:57:32 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:57:32 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:57:32 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:57:33 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:57:33 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:57:33 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:57:33 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:57:33 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:57:33 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:57:33 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:57:33 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:57:33 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:57:33 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:57:33 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:57:33 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:57:33 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:57:33 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:57:33 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:57:34 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:57:34 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:57:34 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:57:34 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:57:34 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:57:34 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:57:34 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-25 09:57:34 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 09:57:34 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:57:34 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:57:35 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:57:35 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:57:35 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:57:35 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:57:35 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:57:35 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:57:35 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:57:35 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:57:35 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:57:35 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:57:35 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:57:35 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:57:35 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:57:35 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:57:35 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:57:35 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:57:35 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:57:36 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:57:36 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:57:36 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:57:36 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:57:36 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:57:36 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:57:36 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:57:36 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:57:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:57:37] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:57:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:57:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:57:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:57:37] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:57:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:57:37] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:57:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:57:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:57:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:57:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:57:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:57:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:57:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:57:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:57:45 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:57:45 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:57:45] "POST /api/scanner/start HTTP/1.1" 200 -
2025-06-25 09:57:45 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:57:45] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:57:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:57:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:57:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:57:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:57:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:57:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:58:01 - src.scanner.real_time_scanner - WARNING - Scanner is already running
2025-06-25 09:58:01 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:58:01] "POST /api/scanner/start HTTP/1.1" 200 -
2025-06-25 09:58:01 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:58:01] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:58:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:58:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:58:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:58:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:58:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:58:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:58:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:58:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:58:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:58:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:58:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:58:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:58:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:58:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:58:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:58:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:58:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:58:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:58:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:58:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:58:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:58:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:03:03 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 10:03:03 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:03:03 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:03:03 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:03:03 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:03:03 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:03:03 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:03:04 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:03:04 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:03:04 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:03:04 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:03:04 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 10:03:04 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 10:03:04 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 10:03:04 - src.ui.app - INFO - Trading components initialized
2025-06-25 10:03:04 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 10:03:04 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:03:04 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:03:04 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:03:04 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:03:04 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:03:04 - __main__ - INFO - Starting real-time scanner...
2025-06-25 10:03:04 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 10:03:04 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 10:03:04 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-25 10:03:04 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 10:03:04 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 10:03:05 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 10:03:05 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:03:05 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:03:05 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:03:05 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:03:05 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:03:05 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:03:06 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:03:06 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:03:06 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:03:06 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:03:06 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 10:03:06 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 10:03:06 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 10:03:06 - src.ui.app - INFO - Trading components initialized
2025-06-25 10:03:06 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 10:03:06 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:03:06 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:03:06 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:03:06 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:03:06 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:03:06 - __main__ - INFO - Starting real-time scanner...
2025-06-25 10:03:06 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 10:03:06 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 10:03:06 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 10:03:06 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 10:03:57 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\test_timeframe_alerts.py', reloading
2025-06-25 10:03:57 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\test_timeframe_alerts.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\utils\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\utils\\logger.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\indicators\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\data\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\scanner\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\ui\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\enhanced_ttm_squeeze.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\tests\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\tests\\test_indicators.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\test_system.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\cli_test.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\test_fractional_fix.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\test_scanner_debug.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\dotenv\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\dotenv\\main.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\numpy\\_typing\\_char_codes.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\numpy\\_typing\\_dtype_like.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\numpy\\_typing\\_nested_sequence.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\numpy\\_typing\\_scalars.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca_trade_api\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca_trade_api\\rest.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca_trade_api\\common.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca_trade_api\\entity.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca_trade_api\\entity_v2.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\trading\\client.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\data\\historical\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\data\\requests.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\data\\timeframe.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\fmp_python\\fmp.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\common\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\common\\utils.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\common\\rest.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\common\\enums.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\trading\\requests.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\trading\\models.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\common\\constants.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\common\\exceptions.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\common\\types.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\data\\historical\\stock.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\data\\models\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\common\\requests.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\data\\enums.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\trading\\enums.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\common\\models.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\_migration.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic_core\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic_core\\core_schema.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\aliases.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\config.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\errors.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\fields.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\main.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\networks.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\types.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\deprecated\\tools.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\_internal\\_repr.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\_internal\\_decorators.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\_internal\\_model_construction.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\annotated_types\\__init__.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\_internal\\_internal_dataclass.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pydantic\\_internal\\_generate_schema.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\data\\models\\bars.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\data\\models\\orderbooks.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\data\\models\\news.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\data\\models\\quotes.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\data\\models\\trades.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\data\\models\\snapshots.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\alpaca\\data\\models\\base.py', reloading
2025-06-25 10:03:58 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 10:03:59 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 10:03:59 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:03:59 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:03:59 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:03:59 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:03:59 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:03:59 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:04:00 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:04:00 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:04:00 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:04:00 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:04:00 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 10:04:00 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 10:04:00 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 10:04:00 - src.ui.app - INFO - Trading components initialized
2025-06-25 10:04:00 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 10:04:00 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:04:00 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:04:00 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:04:00 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:04:00 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:04:00 - __main__ - INFO - Starting real-time scanner...
2025-06-25 10:04:00 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 10:04:00 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 10:04:00 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 10:04:00 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 10:04:04 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:04:04 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:04:04 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:04:04 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:04:04 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:04:06 - src.data.alpaca_provider - WARNING - Insufficient data for AAPL: got 69, requested 100
2025-06-25 10:04:06 - src.scanner.real_time_scanner - INFO - Manual scan result for AAPL: avoid
2025-06-25 10:04:06 - src.data.alpaca_provider - WARNING - Insufficient data for MSFT: got 69, requested 100
2025-06-25 10:04:06 - src.data.alpaca_provider - WARNING - Insufficient data for MSFT: got 69, requested 100
2025-06-25 10:04:06 - src.scanner.real_time_scanner - INFO - Manual scan result for MSFT: avoid
2025-06-25 10:04:06 - src.data.alpaca_provider - WARNING - Insufficient data for TSLA: got 69, requested 100
2025-06-25 10:04:06 - src.scanner.real_time_scanner - INFO - Manual scan result for TSLA: avoid
2025-06-25 10:04:06 - src.data.alpaca_provider - WARNING - Insufficient data for NVDA: got 69, requested 100
2025-06-25 10:04:06 - src.scanner.real_time_scanner - INFO - Manual scan result for NVDA: avoid
2025-06-25 10:04:06 - src.data.alpaca_provider - WARNING - Insufficient data for SPY: got 69, requested 100
2025-06-25 10:04:06 - src.scanner.real_time_scanner - INFO - Manual scan result for SPY: avoid
2025-06-25 10:04:21 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:04:21] "GET / HTTP/1.1" 200 -
2025-06-25 10:04:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:04:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-25 10:04:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:04:22] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-25 10:04:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:04:22] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:04:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:04:22] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:04:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:04:22] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:04:32 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:04:32] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:04:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:04:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:04:42 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:04:42] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:04:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:04:52] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:04:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:04:52] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:04:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:04:52] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:04:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:04:52] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:05:02 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:05:02] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:05:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:05:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:05:12 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:05:12] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:05:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:05:22] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:05:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:05:22] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:05:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:05:22] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:05:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:05:22] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:05:32 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:05:32] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:05:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:05:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:05:42 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:05:42] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:05:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:05:52] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:05:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:05:52] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:05:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:05:52] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:05:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:05:52] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:06:02 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:06:02] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:06:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:06:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:06:12 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:06:12] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:06:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:06:22] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:06:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:06:22] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:06:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:06:22] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:06:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:06:22] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:06:32 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:06:32] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:06:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:06:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:06:42 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:06:42] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:06:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:06:52] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:06:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:06:52] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:06:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:06:52] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:06:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:06:52] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:07:02 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:02] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:07:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:07:07 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 10:07:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:07] "POST /api/scanner/start HTTP/1.1" 200 -
2025-06-25 10:07:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:07:12 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:12] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:07:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:22] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:07:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:22] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:07:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:22] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:07:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:22] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:07:32 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:32] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:07:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:07:42 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:42] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:07:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:52] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:07:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:52] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:07:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:52] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:07:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:07:52] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:08:02 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:08:02] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:08:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:08:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:08:12 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:08:12] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:08:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:08:22] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:08:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:08:22] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:08:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:08:22] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:08:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:08:22] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:08:32 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:08:32] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:08:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:08:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:08:42 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:08:42] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:08:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:08:52] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:08:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:08:52] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:08:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:08:52] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:08:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:08:52] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:09:02 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:09:02] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:09:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:09:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:09:12 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:09:12] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:09:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:09:22] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:09:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:09:22] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:09:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:09:22] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:09:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:09:22] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:09:32 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:09:32] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:09:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:09:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:09:42 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:09:42] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:09:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:09:52] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:09:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:09:52] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:09:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:09:52] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:09:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:09:52] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:10:02 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:10:02] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:10:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:10:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:10:12 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:10:12] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:10:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:10:22] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:10:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:10:22] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:10:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:10:22] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:10:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:10:22] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:10:32 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:10:32] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:10:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:10:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:10:42 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:10:42] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:10:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:10:52] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:10:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:10:52] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:10:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:10:52] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:10:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:10:52] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:11:02 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:11:02] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:11:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:11:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:11:12 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:11:12] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:11:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:11:22] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:11:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:11:22] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:11:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:11:22] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:11:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:11:22] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:11:32 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:11:32] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:11:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:11:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:11:42 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:11:42] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:11:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:11:52] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:11:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:11:52] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:11:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:11:52] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:11:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:11:52] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:12:38 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\scanner\\real_time_scanner.py', reloading
2025-06-25 10:12:38 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\scanner\\real_time_scanner.py', reloading
2025-06-25 10:12:39 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\test_data_fix.py', reloading
2025-06-25 10:12:39 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 10:12:40 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 10:12:46 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:12:46 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:12:47 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:12:47 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:12:47 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:12:47 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:12:47 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:12:47 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:12:47 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:12:47 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:12:47 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 10:12:47 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 10:12:47 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 10:12:47 - src.ui.app - INFO - Trading components initialized
2025-06-25 10:12:47 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 10:12:47 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:12:47 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:12:48 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:12:48 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:12:48 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:12:48 - __main__ - INFO - Starting real-time scanner...
2025-06-25 10:12:48 - src.scanner.real_time_scanner - INFO - Scanner loop started
2025-06-25 10:12:48 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 10:12:48 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 10:12:48 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 10:12:48 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 10:12:48 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:12:48] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:12:48 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:12:48] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:12:48 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:12:48] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:12:48 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:12:48] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:12:49 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\scanner\\real_time_scanner.py', reloading
2025-06-25 10:12:49 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\scanner\\real_time_scanner.py', reloading
2025-06-25 10:12:50 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 10:12:51 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 10:12:51 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:12:51 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:12:51 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:12:51 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:12:51 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:12:51 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:12:51 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:12:51 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:12:51 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:12:52 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:12:52 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 10:12:52 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 10:12:52 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 10:12:52 - src.ui.app - INFO - Trading components initialized
2025-06-25 10:12:52 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 10:12:52 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:12:52 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:12:52 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:12:52 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:12:52 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:12:52 - __main__ - INFO - Starting real-time scanner...
2025-06-25 10:12:52 - src.scanner.real_time_scanner - INFO - Forcing initial universe refresh...
2025-06-25 10:12:52 - src.scanner.real_time_scanner - INFO - Refreshing scan universe...
2025-06-25 10:12:52 - src.scanner.stock_screener - INFO - Refreshing stock universe...
2025-06-25 10:12:52 - src.data.fmp_provider - INFO - Retrieved 503 S&P 500 symbols
2025-06-25 10:12:52 - src.data.data_manager - INFO - Added 503 S&P 500 stocks to universe
2025-06-25 10:12:52 - src.data.data_manager - INFO - Added 50 additional large-cap stocks
2025-06-25 10:12:52 - src.data.data_manager - INFO - Final universe contains 507 stocks
2025-06-25 10:12:52 - src.scanner.stock_screener - INFO - Stock universe contains 507 symbols
2025-06-25 10:12:52 - src.scanner.stock_screener - INFO - Screening 507 stocks...
2025-06-25 10:13:23 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 10:13:23 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:13:23 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:13:23 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:13:23 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:13:23 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:13:23 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:13:23 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:13:23 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:13:23 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:13:23 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:13:23 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 10:13:23 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 10:13:23 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 10:13:23 - src.ui.app - INFO - Trading components initialized
2025-06-25 10:13:23 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 10:13:24 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:13:24 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:13:24 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:13:24 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:13:24 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:13:24 - __main__ - INFO - Starting real-time scanner...
2025-06-25 10:13:24 - src.scanner.real_time_scanner - INFO - Forcing initial universe refresh...
2025-06-25 10:13:24 - src.scanner.real_time_scanner - INFO - Refreshing scan universe...
2025-06-25 10:13:24 - src.scanner.stock_screener - INFO - Refreshing stock universe...
2025-06-25 10:13:24 - src.data.fmp_provider - INFO - Retrieved 503 S&P 500 symbols
2025-06-25 10:13:24 - src.data.data_manager - INFO - Added 503 S&P 500 stocks to universe
2025-06-25 10:13:24 - src.data.data_manager - INFO - Added 50 additional large-cap stocks
2025-06-25 10:13:24 - src.data.data_manager - INFO - Final universe contains 507 stocks
2025-06-25 10:13:24 - src.scanner.stock_screener - INFO - Stock universe contains 507 symbols
2025-06-25 10:13:24 - src.scanner.stock_screener - INFO - Screening 507 stocks...
2025-06-25 10:14:57 - src.scanner.stock_screener - INFO - Found 80 stocks meeting criteria
2025-06-25 10:14:57 - src.scanner.real_time_scanner - INFO - Universe refreshed with 80 symbols
2025-06-25 10:14:57 - src.scanner.real_time_scanner - INFO - Scanner loop started
2025-06-25 10:14:57 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 10:14:57 - src.scanner.real_time_scanner - INFO - Starting scan cycle...
2025-06-25 10:14:57 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 10:14:57 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-25 10:14:57 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 10:14:57 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 10:14:57 - src.data.alpaca_provider - WARNING - Insufficient data for AAPL: got 24, requested 50
2025-06-25 10:14:57 - src.data.alpaca_provider - WARNING - Insufficient data for TSLA: got 27, requested 50
2025-06-25 10:14:57 - src.data.alpaca_provider - WARNING - Insufficient data for GOOGL: got 26, requested 50
2025-06-25 10:14:57 - src.data.alpaca_provider - WARNING - Insufficient data for GOOG: got 23, requested 50
2025-06-25 10:14:58 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 10:14:58 - src.data.alpaca_provider - WARNING - Insufficient data for AMZN: got 24, requested 50
2025-06-25 10:14:58 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:14:58 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:14:58 - src.data.alpaca_provider - WARNING - Insufficient data for AVGO: got 20, requested 50
2025-06-25 10:14:58 - src.data.alpaca_provider - WARNING - Insufficient data for NVDA: got 27, requested 50
2025-06-25 10:14:58 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:14:58 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:14:58 - src.data.alpaca_provider - ERROR - Error getting historical data for BRK-B: invalid symbol: BRK-B
2025-06-25 10:14:58 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:14:58 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:14:58 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:14:58 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:14:58 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:14:58 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:14:58 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 10:14:58 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 10:14:58 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 10:14:58 - src.ui.app - INFO - Trading components initialized
2025-06-25 10:14:58 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 10:14:59 - src.data.alpaca_provider - ERROR - Error getting historical data for BRK-B: invalid symbol: BRK-B
2025-06-25 10:14:59 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 10:14:59 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 10:14:59 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 10:14:59 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 10:14:59 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 10:14:59 - __main__ - INFO - Starting real-time scanner...
2025-06-25 10:14:59 - src.scanner.real_time_scanner - INFO - Forcing initial universe refresh...
2025-06-25 10:14:59 - src.scanner.real_time_scanner - INFO - Refreshing scan universe...
2025-06-25 10:14:59 - src.scanner.stock_screener - INFO - Refreshing stock universe...
2025-06-25 10:14:59 - src.data.fmp_provider - INFO - Retrieved 503 S&P 500 symbols
2025-06-25 10:14:59 - src.data.data_manager - INFO - Added 503 S&P 500 stocks to universe
2025-06-25 10:14:59 - src.data.data_manager - INFO - Added 50 additional large-cap stocks
2025-06-25 10:14:59 - src.data.data_manager - INFO - Final universe contains 507 stocks
2025-06-25 10:14:59 - src.scanner.stock_screener - INFO - Stock universe contains 507 symbols
2025-06-25 10:14:59 - src.scanner.stock_screener - INFO - Screening 507 stocks...
2025-06-25 10:14:59 - src.data.alpaca_provider - ERROR - Error getting historical data for BRK-B: invalid symbol: BRK-B
2025-06-25 10:15:00 - src.data.alpaca_provider - WARNING - Insufficient data for PLTR: got 28, requested 50
2025-06-25 10:15:00 - src.data.alpaca_provider - WARNING - Insufficient data for WMT: got 3, requested 50
2025-06-25 10:15:00 - src.data.alpaca_provider - WARNING - Insufficient data for TSM: got 19, requested 50
2025-06-25 10:15:00 - src.data.alpaca_provider - WARNING - Insufficient data for MSFT: got 14, requested 50
2025-06-25 10:15:00 - src.data.alpaca_provider - WARNING - Insufficient data for CMCSA: got 2, requested 50
2025-06-25 10:15:00 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CMCSA
2025-06-25 10:15:01 - src.data.alpaca_provider - WARNING - Insufficient data for BAC: got 3, requested 50
2025-06-25 10:15:01 - src.data.alpaca_provider - WARNING - Insufficient data for AMD: got 28, requested 50
2025-06-25 10:15:01 - src.data.alpaca_provider - WARNING - Insufficient data for PFE: got 18, requested 50
2025-06-25 10:15:01 - src.data.alpaca_provider - WARNING - Insufficient data for V: got 12, requested 50
2025-06-25 10:15:01 - src.data.alpaca_provider - WARNING - Insufficient data for UNH: got 9, requested 50
2025-06-25 10:15:02 - src.data.alpaca_provider - WARNING - Insufficient data for T: got 3, requested 50
2025-06-25 10:15:02 - src.data.alpaca_provider - WARNING - Insufficient data for XOM: got 10, requested 50
2025-06-25 10:15:02 - src.scanner.real_time_scanner - INFO - ALERT [2]: NVDA - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:15:02 - src.scanner.real_time_scanner - INFO - ALERT [2]: PLTR - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:15:02 - src.data.alpaca_provider - WARNING - Insufficient data for JNJ: got 33, requested 50
2025-06-25 10:15:02 - src.data.alpaca_provider - WARNING - No data returned for JNJ 5Min
2025-06-25 10:15:02 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JNJ
2025-06-25 10:15:02 - src.data.alpaca_provider - WARNING - Insufficient data for UBER: got 21, requested 50
2025-06-25 10:15:02 - src.data.alpaca_provider - WARNING - Insufficient data for ORCL: got 12, requested 50
2025-06-25 10:15:03 - src.data.alpaca_provider - WARNING - Insufficient data for VZ: got 4, requested 50
2025-06-25 10:15:03 - src.data.alpaca_provider - WARNING - Insufficient data for KO: got 3, requested 50
2025-06-25 10:15:03 - src.data.alpaca_provider - WARNING - Insufficient data for ABBV: got 31, requested 50
2025-06-25 10:15:03 - src.data.alpaca_provider - WARNING - No data returned for ABBV 5Min
2025-06-25 10:15:03 - src.data.alpaca_provider - WARNING - Insufficient data for ABBV: got 36, requested 50
2025-06-25 10:15:03 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABBV
2025-06-25 10:15:03 - src.data.alpaca_provider - WARNING - Insufficient data for JPM: got 2, requested 50
2025-06-25 10:15:03 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JPM
2025-06-25 10:15:04 - src.data.alpaca_provider - WARNING - Insufficient data for MRK: got 2, requested 50
2025-06-25 10:15:04 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MRK
2025-06-25 10:15:04 - src.data.alpaca_provider - WARNING - No data returned for WFC 5Min
2025-06-25 10:15:09 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for WFC
2025-06-25 10:15:09 - src.data.alpaca_provider - WARNING - Insufficient data for CSCO: got 5, requested 50
2025-06-25 10:15:09 - src.data.alpaca_provider - WARNING - Insufficient data for MU: got 28, requested 50
2025-06-25 10:15:09 - src.data.alpaca_provider - WARNING - Insufficient data for GE: got 38, requested 50
2025-06-25 10:15:09 - src.data.alpaca_provider - WARNING - No data returned for GE 5Min
2025-06-25 10:15:10 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GE
2025-06-25 10:15:10 - src.data.alpaca_provider - WARNING - No data returned for CRM 5Min
2025-06-25 10:15:10 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CRM
2025-06-25 10:15:10 - src.data.alpaca_provider - WARNING - Insufficient data for PM: got 35, requested 50
2025-06-25 10:15:10 - src.data.alpaca_provider - WARNING - Insufficient data for PM: got 1, requested 50
2025-06-25 10:15:10 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PM
2025-06-25 10:15:11 - src.data.alpaca_provider - WARNING - Insufficient data for PEP: got 3, requested 50
2025-06-25 10:15:11 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PEP
2025-06-25 10:15:11 - src.data.alpaca_provider - WARNING - Insufficient data for NEE: got 1, requested 50
2025-06-25 10:15:11 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for NEE
2025-06-25 10:15:11 - src.data.alpaca_provider - WARNING - Insufficient data for ANET: got 8, requested 50
2025-06-25 10:15:11 - src.data.alpaca_provider - WARNING - Insufficient data for MDT: got 31, requested 50
2025-06-25 10:15:11 - src.data.alpaca_provider - WARNING - No data returned for MDT 5Min
2025-06-25 10:15:12 - src.data.alpaca_provider - WARNING - Insufficient data for MDT: got 38, requested 50
2025-06-25 10:15:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MDT
2025-06-25 10:15:12 - src.data.alpaca_provider - WARNING - Insufficient data for RTX: got 3, requested 50
2025-06-25 10:15:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for RTX
2025-06-25 10:15:12 - src.data.alpaca_provider - WARNING - Insufficient data for HD: got 2, requested 50
2025-06-25 10:15:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for HD
2025-06-25 10:15:12 - src.data.alpaca_provider - WARNING - Insufficient data for MO: got 5, requested 50
2025-06-25 10:15:12 - src.data.alpaca_provider - WARNING - Insufficient data for PG: got 3, requested 50
2025-06-25 10:15:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PG
2025-06-25 10:15:12 - src.data.alpaca_provider - WARNING - Insufficient data for META: got 5, requested 50
2025-06-25 10:15:13 - src.data.alpaca_provider - WARNING - Insufficient data for BSX: got 32, requested 50
2025-06-25 10:15:13 - src.data.alpaca_provider - WARNING - Insufficient data for BSX: got 1, requested 50
2025-06-25 10:15:13 - src.data.alpaca_provider - WARNING - Insufficient data for BSX: got 36, requested 50
2025-06-25 10:15:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for BSX
2025-06-25 10:15:13 - src.data.alpaca_provider - WARNING - Insufficient data for GILD: got 37, requested 50
2025-06-25 10:15:13 - src.data.alpaca_provider - WARNING - No data returned for GILD 5Min
2025-06-25 10:15:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GILD
2025-06-25 10:15:13 - src.data.alpaca_provider - WARNING - Insufficient data for TMUS: got 39, requested 50
2025-06-25 10:15:14 - src.data.alpaca_provider - WARNING - Insufficient data for TMUS: got 1, requested 50
2025-06-25 10:15:14 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TMUS
2025-06-25 10:15:14 - src.data.alpaca_provider - WARNING - Insufficient data for COP: got 3, requested 50
2025-06-25 10:15:14 - src.data.alpaca_provider - WARNING - Insufficient data for QCOM: got 5, requested 50
2025-06-25 10:15:14 - src.data.alpaca_provider - WARNING - Insufficient data for WELL: got 28, requested 50
2025-06-25 10:15:14 - src.data.alpaca_provider - WARNING - No data returned for WELL 5Min
2025-06-25 10:15:14 - src.data.alpaca_provider - WARNING - Insufficient data for WELL: got 30, requested 50
2025-06-25 10:15:14 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for WELL
2025-06-25 10:15:15 - src.data.alpaca_provider - WARNING - Insufficient data for CVX: got 1, requested 50
2025-06-25 10:15:15 - src.data.alpaca_provider - WARNING - Insufficient data for DHR: got 31, requested 50
2025-06-25 10:15:15 - src.data.alpaca_provider - WARNING - Insufficient data for DHR: got 1, requested 50
2025-06-25 10:15:15 - src.data.alpaca_provider - WARNING - Insufficient data for DHR: got 35, requested 50
2025-06-25 10:15:15 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DHR
2025-06-25 10:15:15 - src.data.alpaca_provider - WARNING - Insufficient data for SCHW: got 37, requested 50
2025-06-25 10:15:15 - src.data.alpaca_provider - WARNING - Insufficient data for SCHW: got 1, requested 50
2025-06-25 10:15:15 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for SCHW
2025-06-25 10:15:15 - src.data.alpaca_provider - WARNING - Insufficient data for SBUX: got 1, requested 50
2025-06-25 10:15:15 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for SBUX
2025-06-25 10:15:15 - src.data.alpaca_provider - WARNING - Insufficient data for ABT: got 29, requested 50
2025-06-25 10:15:15 - src.data.alpaca_provider - WARNING - No data returned for ABT 5Min
2025-06-25 10:15:16 - src.data.alpaca_provider - WARNING - Insufficient data for ABT: got 35, requested 50
2025-06-25 10:15:16 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABT
2025-06-25 10:15:16 - src.data.alpaca_provider - WARNING - Insufficient data for DIS: got 1, requested 50
2025-06-25 10:15:16 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DIS
2025-06-25 10:15:16 - src.data.alpaca_provider - WARNING - No data returned for ACN 5Min
2025-06-25 10:15:16 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ACN
2025-06-25 10:15:16 - src.data.alpaca_provider - WARNING - No data returned for AMGN 5Min
2025-06-25 10:15:16 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMGN
2025-06-25 10:15:17 - src.data.alpaca_provider - WARNING - Insufficient data for BA: got 2, requested 50
2025-06-25 10:15:17 - src.data.alpaca_provider - WARNING - Insufficient data for C: got 2, requested 50
2025-06-25 10:15:17 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for C
2025-06-25 10:15:17 - src.data.alpaca_provider - WARNING - Insufficient data for ETN: got 29, requested 50
2025-06-25 10:15:17 - src.data.alpaca_provider - WARNING - No data returned for ETN 5Min
2025-06-25 10:15:17 - src.data.alpaca_provider - WARNING - Insufficient data for ETN: got 33, requested 50
2025-06-25 10:15:17 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ETN
2025-06-25 10:15:17 - src.data.alpaca_provider - WARNING - Insufficient data for TXN: got 35, requested 50
2025-06-25 10:15:17 - src.data.alpaca_provider - WARNING - Insufficient data for TXN: got 1, requested 50
2025-06-25 10:15:17 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TXN
2025-06-25 10:15:18 - src.data.alpaca_provider - WARNING - Insufficient data for PANW: got 1, requested 50
2025-06-25 10:15:18 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PANW
2025-06-25 10:15:18 - src.data.alpaca_provider - WARNING - Insufficient data for ADI: got 30, requested 50
2025-06-25 10:15:18 - src.data.alpaca_provider - WARNING - No data returned for ADI 5Min
2025-06-25 10:15:18 - src.data.alpaca_provider - WARNING - Insufficient data for ADI: got 34, requested 50
2025-06-25 10:15:18 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADI
2025-06-25 10:15:18 - src.data.alpaca_provider - WARNING - Insufficient data for CRWD: got 3, requested 50
2025-06-25 10:15:18 - src.data.alpaca_provider - WARNING - Insufficient data for LRCX: got 2, requested 50
2025-06-25 10:15:18 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LRCX
2025-06-25 10:15:18 - src.data.alpaca_provider - WARNING - Insufficient data for KKR: got 29, requested 50
2025-06-25 10:15:18 - src.data.alpaca_provider - WARNING - No data returned for KKR 5Min
2025-06-25 10:15:19 - src.data.alpaca_provider - WARNING - Insufficient data for KKR: got 35, requested 50
2025-06-25 10:15:19 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for KKR
2025-06-25 10:15:19 - src.data.alpaca_provider - WARNING - No data returned for ADBE 5Min
2025-06-25 10:15:19 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADBE
2025-06-25 10:15:19 - src.data.alpaca_provider - WARNING - Insufficient data for COF: got 36, requested 50
2025-06-25 10:15:19 - src.data.alpaca_provider - WARNING - No data returned for COF 5Min
2025-06-25 10:15:19 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for COF
2025-06-25 10:15:19 - src.data.alpaca_provider - WARNING - Insufficient data for APH: got 36, requested 50
2025-06-25 10:15:20 - src.data.alpaca_provider - WARNING - No data returned for APH 5Min
2025-06-25 10:15:20 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for APH
2025-06-25 10:15:20 - src.data.alpaca_provider - WARNING - Insufficient data for AMAT: got 3, requested 50
2025-06-25 10:15:20 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMAT
2025-06-25 10:15:20 - src.data.alpaca_provider - WARNING - Insufficient data for TJX: got 34, requested 50
2025-06-25 10:15:20 - src.data.alpaca_provider - WARNING - No data returned for TJX 5Min
2025-06-25 10:15:20 - src.data.alpaca_provider - WARNING - Insufficient data for TJX: got 38, requested 50
2025-06-25 10:15:20 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TJX
2025-06-25 10:15:20 - src.data.alpaca_provider - WARNING - Insufficient data for MS: got 39, requested 50
2025-06-25 10:15:20 - src.data.alpaca_provider - WARNING - No data returned for MS 5Min
2025-06-25 10:15:21 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MS
2025-06-25 10:15:21 - src.data.alpaca_provider - WARNING - Insufficient data for PGR: got 32, requested 50
2025-06-25 10:15:21 - src.data.alpaca_provider - WARNING - No data returned for PGR 5Min
2025-06-25 10:15:21 - src.data.alpaca_provider - WARNING - Insufficient data for PGR: got 35, requested 50
2025-06-25 10:15:21 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PGR
2025-06-25 10:15:21 - src.data.alpaca_provider - WARNING - Insufficient data for IBM: got 5, requested 50
2025-06-25 10:15:21 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for IBM
2025-06-25 10:15:21 - src.data.alpaca_provider - WARNING - Insufficient data for MCD: got 38, requested 50
2025-06-25 10:15:22 - src.data.alpaca_provider - WARNING - Insufficient data for MCD: got 1, requested 50
2025-06-25 10:15:22 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MCD
2025-06-25 10:15:22 - src.data.alpaca_provider - WARNING - No data returned for LLY 5Min
2025-06-25 10:15:22 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LLY
2025-06-25 10:15:22 - src.data.alpaca_provider - WARNING - Insufficient data for ADP: got 27, requested 50
2025-06-25 10:15:22 - src.data.alpaca_provider - WARNING - No data returned for ADP 5Min
2025-06-25 10:15:22 - src.data.alpaca_provider - WARNING - Insufficient data for ADP: got 31, requested 50
2025-06-25 10:15:22 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADP
2025-06-25 10:15:22 - src.data.alpaca_provider - WARNING - No data returned for MA 5Min
2025-06-25 10:15:22 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MA
2025-06-25 10:15:23 - src.data.alpaca_provider - WARNING - Insufficient data for ASML: got 7, requested 50
2025-06-25 10:15:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ASML
2025-06-25 10:15:23 - src.data.alpaca_provider - WARNING - No data returned for GEV 5Min
2025-06-25 10:15:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GEV
2025-06-25 10:15:23 - src.scanner.real_time_scanner - INFO - Scan cycle completed in 26.44 seconds
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/PM: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/PM?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/PNC: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/PNC?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/POOL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/POOL?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/PODD: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/PODD?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/PNR: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/PNR?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/PPG: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/PPG?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/PNW: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/PNW?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/PPL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/PPL?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/PRU: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/PRU?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/PSA: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/PSA?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/PSX: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/PSX?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/PWR: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/PWR?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/QCOM: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/QCOM?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/PYPL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/PYPL?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/REG: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/REG?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/RCL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/RCL?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/PTC: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/PTC?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/REGN: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/REGN?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/RJF: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/RJF?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/RMD: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/RMD?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/RF: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/RF?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/RL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/RL?apikey=********************************&timeseries=30
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for profile/NWSA: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/NWSA?apikey=********************************
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for profile/NXPI: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/NXPI?apikey=********************************
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for profile/O: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/O?apikey=********************************
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for profile/ODFL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/ODFL?apikey=********************************
2025-06-25 10:15:52 - src.data.fmp_provider - ERROR - API request failed for profile/OKE: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/OKE?apikey=********************************
2025-06-25 10:15:53 - src.data.fmp_provider - ERROR - API request failed for profile/OMC: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/OMC?apikey=********************************
2025-06-25 10:15:53 - src.data.fmp_provider - ERROR - API request failed for profile/ON: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/ON?apikey=********************************
2025-06-25 10:15:53 - src.data.fmp_provider - ERROR - API request failed for profile/ORCL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/ORCL?apikey=********************************
2025-06-25 10:15:53 - src.data.fmp_provider - ERROR - API request failed for profile/ORLY: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/ORLY?apikey=********************************
2025-06-25 10:15:53 - src.data.fmp_provider - ERROR - API request failed for profile/OTIS: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/OTIS?apikey=********************************
2025-06-25 10:15:53 - src.data.fmp_provider - ERROR - API request failed for profile/OXY: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/OXY?apikey=********************************
2025-06-25 10:15:53 - src.data.fmp_provider - ERROR - API request failed for profile/PANW: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PANW?apikey=********************************
2025-06-25 10:15:53 - src.data.fmp_provider - ERROR - API request failed for profile/PARA: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PARA?apikey=********************************
2025-06-25 10:15:53 - src.data.fmp_provider - ERROR - API request failed for profile/PAYC: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PAYC?apikey=********************************
2025-06-25 10:15:53 - src.data.fmp_provider - ERROR - API request failed for profile/PAYX: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PAYX?apikey=********************************
2025-06-25 10:15:54 - src.data.fmp_provider - ERROR - API request failed for profile/PCAR: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PCAR?apikey=********************************
2025-06-25 10:15:54 - src.data.fmp_provider - ERROR - API request failed for profile/PCG: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PCG?apikey=********************************
2025-06-25 10:15:54 - src.data.fmp_provider - ERROR - API request failed for profile/PEG: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PEG?apikey=********************************
2025-06-25 10:15:54 - src.data.fmp_provider - ERROR - API request failed for profile/PEP: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PEP?apikey=********************************
2025-06-25 10:15:54 - src.data.fmp_provider - ERROR - API request failed for profile/PFE: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PFE?apikey=********************************
2025-06-25 10:15:54 - src.data.fmp_provider - ERROR - API request failed for profile/PFG: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PFG?apikey=********************************
2025-06-25 10:15:54 - src.data.fmp_provider - ERROR - API request failed for profile/PG: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PG?apikey=********************************
2025-06-25 10:15:54 - src.data.fmp_provider - ERROR - API request failed for profile/PGR: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PGR?apikey=********************************
2025-06-25 10:15:54 - src.data.fmp_provider - ERROR - API request failed for profile/PH: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PH?apikey=********************************
2025-06-25 10:15:55 - src.data.fmp_provider - ERROR - API request failed for profile/PHM: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PHM?apikey=********************************
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for profile/PKG: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PKG?apikey=********************************
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for profile/PLD: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PLD?apikey=********************************
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for profile/PLTR: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/profile/PLTR?apikey=********************************
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/ROST: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/ROST?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SBAC: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SBAC?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/RTX: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/RTX?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/ROL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/ROL?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/RSG: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/RSG?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SCHW: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SCHW?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SBUX: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SBUX?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/ROP: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/ROP?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/RVTY: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/RVTY?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SMCI: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SMCI?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SNPS: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SNPS?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SHW: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SHW?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SPG: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SPG?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SNA: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SNA?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SO: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SO?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SOLV: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SOLV?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SJM: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SJM?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/ROK: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/ROK?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SLB: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SLB?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/STLD: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/STLD?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SWKS: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SWKS?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SRE: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SRE?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/STX: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/STX?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SW: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SW?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SWK: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SWK?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/STZ: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/STZ?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/STT: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/STT?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/STE: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/STE?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SPGI: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SPGI?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SYY: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SYY?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SYF: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SYF?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TECH: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TECH?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TDY: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TDY?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TEL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TEL?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TDG: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TDG?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/T: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/T?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TAP: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TAP?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/SYK: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/SYK?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TER: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TER?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TPL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TPL?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TFC: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TFC?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TMO: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TMO?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TGT: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TGT?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TRGP: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TRGP?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TMUS: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TMUS?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TRMB: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TRMB?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TPR: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TPR?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TJX: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TJX?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TKO: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TKO?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TROW: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TROW?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TTWO: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TTWO?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TRV: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TRV?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TT: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TT?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TXN: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TXN?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TSM: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TSM?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TXT: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TXT?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TSLA: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TSLA?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TYL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TYL?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TSCO: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TSCO?apikey=********************************&timeseries=30
2025-06-25 10:15:56 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/TSN: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/TSN?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/USB: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/USB?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/UNP: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/UNP?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/UBER: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/UBER?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/UHS: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/UHS?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/UAL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/UAL?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/UNH: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/UNH?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/UPS: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/UPS?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/URI: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/URI?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/UDR: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/UDR?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/ULTA: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/ULTA?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/VRSK: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/VRSK?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/VST: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/VST?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/VRSN: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/VRSN?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/VLTO: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/VLTO?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/VLO: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/VLO?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/VMC: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/VMC?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/VICI: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/VICI?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/VRTX: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/VRTX?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/V: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/V?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/VTRS: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/VTRS?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WEC: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WEC?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WAT: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WAT?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/VZ: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/VZ?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WBD: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WBD?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/VTR: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/VTR?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WDAY: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WDAY?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WBA: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WBA?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WAB: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WAB?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WDC: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WDC?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WMT: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WMT?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WRB: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WRB?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WY: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WY?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WELL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WELL?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WSM: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WSM?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WST: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WST?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WTW: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WTW?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WM: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WM?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WMB: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WMB?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WFC: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WFC?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/WYNN: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/WYNN?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/ZBH: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/ZBH?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/XOM: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/XOM?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/XEL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/XEL?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/YUM: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/YUM?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/XYL: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/XYL?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/ZBRA: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/ZBRA?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.data.fmp_provider - ERROR - API request failed for historical-price-full/ZTS: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-price-full/ZTS?apikey=********************************&timeseries=30
2025-06-25 10:15:57 - src.scanner.stock_screener - INFO - Found 54 stocks meeting criteria
2025-06-25 10:15:57 - src.scanner.real_time_scanner - INFO - Universe refreshed with 54 symbols
2025-06-25 10:15:57 - src.scanner.real_time_scanner - INFO - Scanner loop started
2025-06-25 10:15:57 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 10:15:57 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 10:15:57 - src.scanner.real_time_scanner - INFO - Starting scan cycle...
2025-06-25 10:15:57 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 10:15:57 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 10:15:57 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:15:57] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:15:57 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:15:57] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:15:57 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:15:57] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:15:57 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:15:57] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:15:57 - src.data.alpaca_provider - WARNING - Insufficient data for AAPL: got 25, requested 50
2025-06-25 10:15:58 - src.data.alpaca_provider - WARNING - Insufficient data for GOOGL: got 27, requested 50
2025-06-25 10:15:58 - src.data.alpaca_provider - WARNING - Insufficient data for GOOG: got 23, requested 50
2025-06-25 10:15:58 - src.data.alpaca_provider - WARNING - Insufficient data for AMZN: got 25, requested 50
2025-06-25 10:15:58 - src.data.alpaca_provider - WARNING - Insufficient data for AVGO: got 20, requested 50
2025-06-25 10:15:58 - src.data.alpaca_provider - WARNING - Insufficient data for NVDA: got 28, requested 50
2025-06-25 10:15:58 - src.data.alpaca_provider - ERROR - Error getting historical data for BRK-B: invalid symbol: BRK-B
2025-06-25 10:15:59 - src.data.fmp_provider - ERROR - API request failed for historical-chart/15min/BRK-B: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/15min/BRK-B?apikey=********************************
2025-06-25 10:15:59 - src.data.alpaca_provider - ERROR - Error getting historical data for BRK-B: invalid symbol: BRK-B
2025-06-25 10:15:59 - src.data.fmp_provider - ERROR - API request failed for historical-chart/5min/BRK-B: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/5min/BRK-B?apikey=********************************
2025-06-25 10:15:59 - src.data.alpaca_provider - ERROR - Error getting historical data for BRK-B: invalid symbol: BRK-B
2025-06-25 10:15:59 - src.data.fmp_provider - ERROR - API request failed for historical-chart/30min/BRK-B: 429 Client Error: Too Many Requests for url: https://financialmodelingprep.com/api/v3/historical-chart/30min/BRK-B?apikey=********************************
2025-06-25 10:15:59 - src.data.alpaca_provider - WARNING - Insufficient data for MSFT: got 14, requested 50
2025-06-25 10:15:59 - src.data.alpaca_provider - WARNING - Insufficient data for CMCSA: got 2, requested 50
2025-06-25 10:16:00 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CMCSA
2025-06-25 10:16:00 - src.data.alpaca_provider - WARNING - Insufficient data for BAC: got 3, requested 50
2025-06-25 10:16:00 - src.data.alpaca_provider - WARNING - Insufficient data for AMD: got 28, requested 50
2025-06-25 10:16:00 - src.data.alpaca_provider - WARNING - Insufficient data for JNJ: got 33, requested 50
2025-06-25 10:16:00 - src.data.alpaca_provider - WARNING - No data returned for JNJ 5Min
2025-06-25 10:16:00 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JNJ
2025-06-25 10:16:00 - src.data.alpaca_provider - WARNING - Insufficient data for KO: got 3, requested 50
2025-06-25 10:16:00 - src.data.alpaca_provider - WARNING - Insufficient data for ABBV: got 31, requested 50
2025-06-25 10:16:01 - src.data.alpaca_provider - WARNING - No data returned for ABBV 5Min
2025-06-25 10:16:01 - src.data.alpaca_provider - WARNING - Insufficient data for ABBV: got 36, requested 50
2025-06-25 10:16:01 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABBV
2025-06-25 10:16:01 - src.data.alpaca_provider - WARNING - Insufficient data for JPM: got 2, requested 50
2025-06-25 10:16:01 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JPM
2025-06-25 10:16:01 - src.data.alpaca_provider - WARNING - Insufficient data for MRK: got 2, requested 50
2025-06-25 10:16:01 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MRK
2025-06-25 10:16:01 - src.data.alpaca_provider - WARNING - Insufficient data for CSCO: got 5, requested 50
2025-06-25 10:16:01 - src.data.alpaca_provider - WARNING - Insufficient data for MU: got 28, requested 50
2025-06-25 10:16:02 - src.data.alpaca_provider - WARNING - Insufficient data for GE: got 38, requested 50
2025-06-25 10:16:02 - src.data.alpaca_provider - WARNING - No data returned for GE 5Min
2025-06-25 10:16:02 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GE
2025-06-25 10:16:02 - src.data.alpaca_provider - WARNING - No data returned for CRM 5Min
2025-06-25 10:16:02 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CRM
2025-06-25 10:16:02 - src.scanner.real_time_scanner - INFO - ALERT [2]: NVDA - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:16:02 - src.data.alpaca_provider - WARNING - Insufficient data for NEE: got 1, requested 50
2025-06-25 10:16:02 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for NEE
2025-06-25 10:16:02 - src.data.alpaca_provider - WARNING - Insufficient data for ANET: got 8, requested 50
2025-06-25 10:16:02 - src.data.alpaca_provider - WARNING - Insufficient data for MDT: got 31, requested 50
2025-06-25 10:16:02 - src.data.alpaca_provider - WARNING - No data returned for MDT 5Min
2025-06-25 10:16:03 - src.data.alpaca_provider - WARNING - Insufficient data for MDT: got 38, requested 50
2025-06-25 10:16:03 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MDT
2025-06-25 10:16:03 - src.data.alpaca_provider - WARNING - Insufficient data for HD: got 2, requested 50
2025-06-25 10:16:03 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for HD
2025-06-25 10:16:03 - src.data.alpaca_provider - WARNING - Insufficient data for MO: got 5, requested 50
2025-06-25 10:16:03 - src.data.alpaca_provider - WARNING - Insufficient data for META: got 5, requested 50
2025-06-25 10:16:03 - src.data.alpaca_provider - WARNING - Insufficient data for BSX: got 32, requested 50
2025-06-25 10:16:03 - src.data.alpaca_provider - WARNING - Insufficient data for BSX: got 1, requested 50
2025-06-25 10:16:03 - src.data.alpaca_provider - WARNING - Insufficient data for BSX: got 36, requested 50
2025-06-25 10:16:03 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for BSX
2025-06-25 10:16:03 - src.data.alpaca_provider - WARNING - Insufficient data for GILD: got 37, requested 50
2025-06-25 10:16:03 - src.data.alpaca_provider - WARNING - No data returned for GILD 5Min
2025-06-25 10:16:04 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GILD
2025-06-25 10:16:04 - src.data.alpaca_provider - WARNING - Insufficient data for COP: got 3, requested 50
2025-06-25 10:16:04 - src.data.alpaca_provider - WARNING - Insufficient data for CVX: got 1, requested 50
2025-06-25 10:16:04 - src.data.alpaca_provider - WARNING - Insufficient data for DHR: got 31, requested 50
2025-06-25 10:16:04 - src.data.alpaca_provider - WARNING - Insufficient data for DHR: got 1, requested 50
2025-06-25 10:16:04 - src.data.alpaca_provider - WARNING - Insufficient data for DHR: got 35, requested 50
2025-06-25 10:16:04 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DHR
2025-06-25 10:16:04 - src.data.alpaca_provider - WARNING - Insufficient data for ABT: got 29, requested 50
2025-06-25 10:16:04 - src.data.alpaca_provider - WARNING - No data returned for ABT 5Min
2025-06-25 10:16:04 - src.data.alpaca_provider - WARNING - Insufficient data for ABT: got 35, requested 50
2025-06-25 10:16:04 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABT
2025-06-25 10:16:04 - src.data.alpaca_provider - WARNING - Insufficient data for DIS: got 1, requested 50
2025-06-25 10:16:04 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DIS
2025-06-25 10:16:05 - src.data.alpaca_provider - WARNING - No data returned for ACN 5Min
2025-06-25 10:16:05 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ACN
2025-06-25 10:16:05 - src.data.alpaca_provider - WARNING - No data returned for AMGN 5Min
2025-06-25 10:16:05 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMGN
2025-06-25 10:16:05 - src.data.alpaca_provider - WARNING - Insufficient data for BA: got 2, requested 50
2025-06-25 10:16:05 - src.data.alpaca_provider - WARNING - Insufficient data for C: got 2, requested 50
2025-06-25 10:16:05 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for C
2025-06-25 10:16:06 - src.data.alpaca_provider - WARNING - Insufficient data for ETN: got 29, requested 50
2025-06-25 10:16:06 - src.data.alpaca_provider - WARNING - No data returned for ETN 5Min
2025-06-25 10:16:06 - src.data.alpaca_provider - WARNING - Insufficient data for ETN: got 33, requested 50
2025-06-25 10:16:06 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ETN
2025-06-25 10:16:06 - src.data.alpaca_provider - WARNING - Insufficient data for ADI: got 30, requested 50
2025-06-25 10:16:06 - src.data.alpaca_provider - WARNING - No data returned for ADI 5Min
2025-06-25 10:16:06 - src.data.alpaca_provider - WARNING - Insufficient data for ADI: got 34, requested 50
2025-06-25 10:16:06 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADI
2025-06-25 10:16:06 - src.data.alpaca_provider - WARNING - Insufficient data for CRWD: got 3, requested 50
2025-06-25 10:16:06 - src.data.alpaca_provider - WARNING - Insufficient data for LRCX: got 2, requested 50
2025-06-25 10:16:06 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LRCX
2025-06-25 10:16:06 - src.data.alpaca_provider - WARNING - Insufficient data for KKR: got 29, requested 50
2025-06-25 10:16:06 - src.data.alpaca_provider - WARNING - No data returned for KKR 5Min
2025-06-25 10:16:07 - src.data.alpaca_provider - WARNING - Insufficient data for KKR: got 35, requested 50
2025-06-25 10:16:07 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for KKR
2025-06-25 10:16:07 - src.data.alpaca_provider - WARNING - No data returned for ADBE 5Min
2025-06-25 10:16:07 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADBE
2025-06-25 10:16:07 - src.data.alpaca_provider - WARNING - Insufficient data for COF: got 36, requested 50
2025-06-25 10:16:07 - src.data.alpaca_provider - WARNING - No data returned for COF 5Min
2025-06-25 10:16:07 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for COF
2025-06-25 10:16:07 - src.data.alpaca_provider - WARNING - Insufficient data for APH: got 36, requested 50
2025-06-25 10:16:07 - src.data.alpaca_provider - WARNING - No data returned for APH 5Min
2025-06-25 10:16:08 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for APH
2025-06-25 10:16:08 - src.data.alpaca_provider - WARNING - Insufficient data for AMAT: got 3, requested 50
2025-06-25 10:16:08 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMAT
2025-06-25 10:16:08 - src.data.alpaca_provider - WARNING - Insufficient data for MS: got 39, requested 50
2025-06-25 10:16:08 - src.data.alpaca_provider - WARNING - No data returned for MS 5Min
2025-06-25 10:16:08 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MS
2025-06-25 10:16:08 - src.data.alpaca_provider - WARNING - Insufficient data for IBM: got 5, requested 50
2025-06-25 10:16:08 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for IBM
2025-06-25 10:16:08 - src.data.alpaca_provider - WARNING - Insufficient data for MCD: got 38, requested 50
2025-06-25 10:16:08 - src.data.alpaca_provider - WARNING - Insufficient data for MCD: got 1, requested 50
2025-06-25 10:16:08 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MCD
2025-06-25 10:16:08 - src.data.alpaca_provider - WARNING - No data returned for LLY 5Min
2025-06-25 10:16:09 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LLY
2025-06-25 10:16:09 - src.data.alpaca_provider - WARNING - Insufficient data for ADP: got 27, requested 50
2025-06-25 10:16:09 - src.data.alpaca_provider - WARNING - No data returned for ADP 5Min
2025-06-25 10:16:09 - src.data.alpaca_provider - WARNING - Insufficient data for ADP: got 31, requested 50
2025-06-25 10:16:09 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADP
2025-06-25 10:16:09 - src.data.alpaca_provider - WARNING - No data returned for MA 5Min
2025-06-25 10:16:10 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MA
2025-06-25 10:16:10 - src.data.alpaca_provider - WARNING - Insufficient data for ASML: got 7, requested 50
2025-06-25 10:16:10 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ASML
2025-06-25 10:16:10 - src.data.alpaca_provider - WARNING - No data returned for GEV 5Min
2025-06-25 10:16:10 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GEV
2025-06-25 10:16:10 - src.scanner.real_time_scanner - INFO - Scan cycle completed in 13.23 seconds
2025-06-25 10:16:23 - src.scanner.real_time_scanner - INFO - Starting scan cycle...
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CMCSA
2025-06-25 10:16:23 - src.scanner.real_time_scanner - INFO - ALERT [2]: NVDA - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:16:23 - src.scanner.real_time_scanner - INFO - ALERT [2]: PLTR - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JNJ
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABBV
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JPM
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MRK
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for WFC
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GE
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CRM
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PM
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PEP
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for NEE
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MDT
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for RTX
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for HD
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PG
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for BSX
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GILD
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TMUS
2025-06-25 10:16:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for WELL
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DHR
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for SCHW
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for SBUX
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABT
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DIS
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ACN
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMGN
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for C
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ETN
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TXN
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PANW
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADI
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LRCX
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for KKR
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADBE
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for COF
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for APH
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMAT
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TJX
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MS
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PGR
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for IBM
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MCD
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LLY
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADP
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MA
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ASML
2025-06-25 10:16:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GEV
2025-06-25 10:16:24 - src.scanner.real_time_scanner - INFO - Scan cycle completed in 0.52 seconds
2025-06-25 10:16:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:16:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:16:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:16:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:16:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:16:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:16:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:16:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:17:10 - src.scanner.real_time_scanner - INFO - Starting scan cycle...
2025-06-25 10:17:10 - src.data.alpaca_provider - ERROR - Error getting historical data for BRK-B: invalid symbol: BRK-B
2025-06-25 10:17:11 - src.data.alpaca_provider - ERROR - Error getting historical data for BRK-B: invalid symbol: BRK-B
2025-06-25 10:17:11 - src.data.alpaca_provider - ERROR - Error getting historical data for BRK-B: invalid symbol: BRK-B
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CMCSA
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JNJ
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABBV
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JPM
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MRK
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GE
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CRM
2025-06-25 10:17:12 - src.scanner.real_time_scanner - INFO - ALERT [2]: NVDA - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for NEE
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MDT
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for HD
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for BSX
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GILD
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DHR
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABT
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DIS
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ACN
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMGN
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for C
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ETN
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADI
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LRCX
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for KKR
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADBE
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for COF
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for APH
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMAT
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MS
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for IBM
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MCD
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LLY
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADP
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MA
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ASML
2025-06-25 10:17:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GEV
2025-06-25 10:17:12 - src.scanner.real_time_scanner - INFO - Scan cycle completed in 1.43 seconds
2025-06-25 10:17:24 - src.scanner.real_time_scanner - INFO - Starting scan cycle...
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CMCSA
2025-06-25 10:17:24 - src.scanner.real_time_scanner - INFO - ALERT [2]: NVDA - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:17:24 - src.scanner.real_time_scanner - INFO - ALERT [2]: PLTR - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JNJ
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABBV
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JPM
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MRK
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for WFC
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GE
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CRM
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PM
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PEP
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for NEE
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MDT
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for RTX
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for HD
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PG
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for BSX
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GILD
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TMUS
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for WELL
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DHR
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for SCHW
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for SBUX
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABT
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DIS
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ACN
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMGN
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for C
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ETN
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TXN
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PANW
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADI
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LRCX
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for KKR
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADBE
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for COF
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for APH
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMAT
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TJX
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MS
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PGR
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for IBM
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MCD
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LLY
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADP
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MA
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ASML
2025-06-25 10:17:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GEV
2025-06-25 10:17:24 - src.scanner.real_time_scanner - INFO - Scan cycle completed in 0.51 seconds
2025-06-25 10:17:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:17:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:17:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:17:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:17:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:17:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:17:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:17:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:18:12 - src.scanner.real_time_scanner - INFO - Starting scan cycle...
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CMCSA
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JNJ
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABBV
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JPM
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MRK
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GE
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CRM
2025-06-25 10:18:12 - src.scanner.real_time_scanner - INFO - ALERT [2]: NVDA - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for NEE
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MDT
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for HD
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for BSX
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GILD
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DHR
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABT
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DIS
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ACN
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMGN
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for C
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ETN
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADI
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LRCX
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for KKR
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADBE
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for COF
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for APH
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMAT
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MS
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for IBM
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MCD
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LLY
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADP
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MA
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ASML
2025-06-25 10:18:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GEV
2025-06-25 10:18:12 - src.scanner.real_time_scanner - INFO - Scan cycle completed in 0.32 seconds
2025-06-25 10:18:24 - src.scanner.real_time_scanner - INFO - Starting scan cycle...
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CMCSA
2025-06-25 10:18:24 - src.scanner.real_time_scanner - INFO - ALERT [2]: NVDA - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:18:24 - src.scanner.real_time_scanner - INFO - ALERT [2]: PLTR - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JNJ
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABBV
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JPM
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MRK
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for WFC
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GE
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CRM
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PM
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PEP
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for NEE
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MDT
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for RTX
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for HD
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PG
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for BSX
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GILD
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TMUS
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for WELL
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DHR
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for SCHW
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for SBUX
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABT
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DIS
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ACN
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMGN
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for C
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ETN
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TXN
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PANW
2025-06-25 10:18:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADI
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LRCX
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for KKR
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADBE
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for COF
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for APH
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMAT
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TJX
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MS
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PGR
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for IBM
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MCD
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LLY
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADP
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MA
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ASML
2025-06-25 10:18:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GEV
2025-06-25 10:18:25 - src.scanner.real_time_scanner - INFO - Scan cycle completed in 0.46 seconds
2025-06-25 10:18:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:18:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:18:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:18:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:18:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:18:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:18:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:18:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:19:12 - src.scanner.real_time_scanner - INFO - Starting scan cycle...
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CMCSA
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JNJ
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABBV
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JPM
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MRK
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GE
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CRM
2025-06-25 10:19:12 - src.scanner.real_time_scanner - INFO - ALERT [2]: NVDA - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for NEE
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MDT
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for HD
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for BSX
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GILD
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DHR
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABT
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DIS
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ACN
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMGN
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for C
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ETN
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADI
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LRCX
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for KKR
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADBE
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for COF
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for APH
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMAT
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MS
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for IBM
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MCD
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LLY
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADP
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MA
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ASML
2025-06-25 10:19:12 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GEV
2025-06-25 10:19:12 - src.scanner.real_time_scanner - INFO - Scan cycle completed in 0.34 seconds
2025-06-25 10:19:25 - src.scanner.real_time_scanner - INFO - Starting scan cycle...
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CMCSA
2025-06-25 10:19:25 - src.scanner.real_time_scanner - INFO - ALERT [2]: NVDA - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:19:25 - src.scanner.real_time_scanner - INFO - ALERT [2]: PLTR - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JNJ
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABBV
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JPM
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MRK
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for WFC
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GE
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CRM
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PM
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PEP
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for NEE
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MDT
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for RTX
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for HD
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PG
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for BSX
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GILD
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TMUS
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for WELL
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DHR
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for SCHW
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for SBUX
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABT
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DIS
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ACN
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMGN
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for C
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ETN
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TXN
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PANW
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADI
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LRCX
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for KKR
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADBE
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for COF
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for APH
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMAT
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TJX
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MS
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PGR
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for IBM
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MCD
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LLY
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADP
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MA
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ASML
2025-06-25 10:19:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GEV
2025-06-25 10:19:25 - src.scanner.real_time_scanner - INFO - Scan cycle completed in 0.47 seconds
2025-06-25 10:19:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:19:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:19:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:19:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:19:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:19:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:19:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:19:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:20:12 - src.scanner.real_time_scanner - INFO - Starting scan cycle...
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CMCSA
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JNJ
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABBV
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JPM
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MRK
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GE
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CRM
2025-06-25 10:20:13 - src.scanner.real_time_scanner - INFO - ALERT [2]: NVDA - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for NEE
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MDT
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for HD
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for BSX
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GILD
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DHR
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABT
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DIS
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ACN
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMGN
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for C
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ETN
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADI
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LRCX
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for KKR
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADBE
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for COF
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for APH
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMAT
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MS
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for IBM
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MCD
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LLY
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADP
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MA
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ASML
2025-06-25 10:20:13 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GEV
2025-06-25 10:20:13 - src.scanner.real_time_scanner - INFO - Scan cycle completed in 0.34 seconds
2025-06-25 10:20:25 - src.scanner.real_time_scanner - INFO - Starting scan cycle...
2025-06-25 10:20:25 - src.data.alpaca_provider - WARNING - Insufficient data for AAPL: got 26, requested 50
2025-06-25 10:20:25 - src.data.alpaca_provider - WARNING - Insufficient data for TSLA: got 29, requested 50
2025-06-25 10:20:26 - src.data.alpaca_provider - WARNING - Insufficient data for GOOGL: got 27, requested 50
2025-06-25 10:20:26 - src.data.alpaca_provider - WARNING - Insufficient data for GOOG: got 23, requested 50
2025-06-25 10:20:26 - src.data.alpaca_provider - WARNING - Insufficient data for AMZN: got 26, requested 50
2025-06-25 10:20:26 - src.data.alpaca_provider - WARNING - Insufficient data for AVGO: got 21, requested 50
2025-06-25 10:20:26 - src.data.alpaca_provider - WARNING - Insufficient data for NVDA: got 29, requested 50
2025-06-25 10:20:26 - src.data.alpaca_provider - ERROR - Error getting historical data for BRK-B: invalid symbol: BRK-B
2025-06-25 10:20:27 - src.data.alpaca_provider - ERROR - Error getting historical data for BRK-B: invalid symbol: BRK-B
2025-06-25 10:20:27 - src.data.alpaca_provider - ERROR - Error getting historical data for BRK-B: invalid symbol: BRK-B
2025-06-25 10:20:28 - src.data.alpaca_provider - WARNING - Insufficient data for PLTR: got 29, requested 50
2025-06-25 10:20:28 - src.data.alpaca_provider - WARNING - Insufficient data for WMT: got 3, requested 50
2025-06-25 10:20:28 - src.data.alpaca_provider - WARNING - Insufficient data for TSM: got 20, requested 50
2025-06-25 10:20:28 - src.data.alpaca_provider - WARNING - Insufficient data for MSFT: got 15, requested 50
2025-06-25 10:20:28 - src.data.alpaca_provider - WARNING - Insufficient data for CMCSA: got 2, requested 50
2025-06-25 10:20:28 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CMCSA
2025-06-25 10:20:29 - src.data.alpaca_provider - WARNING - Insufficient data for BAC: got 3, requested 50
2025-06-25 10:20:29 - src.data.alpaca_provider - WARNING - Insufficient data for AMD: got 29, requested 50
2025-06-25 10:20:29 - src.data.alpaca_provider - WARNING - Insufficient data for PFE: got 18, requested 50
2025-06-25 10:20:29 - src.data.alpaca_provider - WARNING - Insufficient data for V: got 12, requested 50
2025-06-25 10:20:29 - src.data.alpaca_provider - WARNING - Insufficient data for UNH: got 9, requested 50
2025-06-25 10:20:30 - src.data.alpaca_provider - WARNING - Insufficient data for T: got 3, requested 50
2025-06-25 10:20:30 - src.data.alpaca_provider - WARNING - Insufficient data for XOM: got 10, requested 50
2025-06-25 10:20:30 - src.scanner.real_time_scanner - INFO - ALERT [2]: NVDA - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:20:30 - src.scanner.real_time_scanner - INFO - ALERT [2]: PLTR - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:20:30 - src.data.alpaca_provider - WARNING - Insufficient data for JNJ: got 33, requested 50
2025-06-25 10:20:30 - src.data.alpaca_provider - WARNING - No data returned for JNJ 5Min
2025-06-25 10:20:30 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JNJ
2025-06-25 10:20:30 - src.data.alpaca_provider - WARNING - Insufficient data for UBER: got 21, requested 50
2025-06-25 10:20:30 - src.data.alpaca_provider - WARNING - Insufficient data for ORCL: got 13, requested 50
2025-06-25 10:20:31 - src.data.alpaca_provider - WARNING - Insufficient data for VZ: got 4, requested 50
2025-06-25 10:20:31 - src.data.alpaca_provider - WARNING - Insufficient data for KO: got 3, requested 50
2025-06-25 10:20:31 - src.data.alpaca_provider - WARNING - Insufficient data for ABBV: got 31, requested 50
2025-06-25 10:20:31 - src.data.alpaca_provider - WARNING - No data returned for ABBV 5Min
2025-06-25 10:20:31 - src.data.alpaca_provider - WARNING - Insufficient data for ABBV: got 36, requested 50
2025-06-25 10:20:31 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABBV
2025-06-25 10:20:31 - src.data.alpaca_provider - WARNING - Insufficient data for JPM: got 2, requested 50
2025-06-25 10:20:31 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JPM
2025-06-25 10:20:31 - src.data.alpaca_provider - WARNING - Insufficient data for MRK: got 2, requested 50
2025-06-25 10:20:31 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MRK
2025-06-25 10:20:31 - src.data.alpaca_provider - WARNING - No data returned for WFC 5Min
2025-06-25 10:20:32 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for WFC
2025-06-25 10:20:32 - src.data.alpaca_provider - WARNING - Insufficient data for CSCO: got 6, requested 50
2025-06-25 10:20:32 - src.data.alpaca_provider - WARNING - Insufficient data for MU: got 29, requested 50
2025-06-25 10:20:32 - src.data.alpaca_provider - WARNING - Insufficient data for GE: got 38, requested 50
2025-06-25 10:20:32 - src.data.alpaca_provider - WARNING - No data returned for GE 5Min
2025-06-25 10:20:32 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GE
2025-06-25 10:20:32 - src.data.alpaca_provider - WARNING - No data returned for CRM 5Min
2025-06-25 10:20:32 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CRM
2025-06-25 10:20:33 - src.data.alpaca_provider - WARNING - Insufficient data for PM: got 35, requested 50
2025-06-25 10:20:33 - src.data.alpaca_provider - WARNING - Insufficient data for PM: got 1, requested 50
2025-06-25 10:20:33 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PM
2025-06-25 10:20:33 - src.data.alpaca_provider - WARNING - Insufficient data for PEP: got 4, requested 50
2025-06-25 10:20:33 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PEP
2025-06-25 10:20:33 - src.data.alpaca_provider - WARNING - Insufficient data for NEE: got 1, requested 50
2025-06-25 10:20:33 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for NEE
2025-06-25 10:20:33 - src.data.alpaca_provider - WARNING - Insufficient data for ANET: got 8, requested 50
2025-06-25 10:20:33 - src.data.alpaca_provider - WARNING - Insufficient data for MDT: got 31, requested 50
2025-06-25 10:20:33 - src.data.alpaca_provider - WARNING - No data returned for MDT 5Min
2025-06-25 10:20:33 - src.data.alpaca_provider - WARNING - Insufficient data for MDT: got 38, requested 50
2025-06-25 10:20:33 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MDT
2025-06-25 10:20:33 - src.data.alpaca_provider - WARNING - Insufficient data for RTX: got 3, requested 50
2025-06-25 10:20:34 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for RTX
2025-06-25 10:20:34 - src.data.alpaca_provider - WARNING - Insufficient data for HD: got 2, requested 50
2025-06-25 10:20:34 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for HD
2025-06-25 10:20:34 - src.data.alpaca_provider - WARNING - Insufficient data for MO: got 6, requested 50
2025-06-25 10:20:34 - src.data.alpaca_provider - WARNING - Insufficient data for PG: got 4, requested 50
2025-06-25 10:20:34 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PG
2025-06-25 10:20:34 - src.data.alpaca_provider - WARNING - Insufficient data for META: got 6, requested 50
2025-06-25 10:20:34 - src.data.alpaca_provider - WARNING - Insufficient data for BSX: got 32, requested 50
2025-06-25 10:20:34 - src.data.alpaca_provider - WARNING - Insufficient data for BSX: got 1, requested 50
2025-06-25 10:20:34 - src.data.alpaca_provider - WARNING - Insufficient data for BSX: got 36, requested 50
2025-06-25 10:20:34 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for BSX
2025-06-25 10:20:34 - src.data.alpaca_provider - WARNING - Insufficient data for GILD: got 37, requested 50
2025-06-25 10:20:34 - src.data.alpaca_provider - WARNING - No data returned for GILD 5Min
2025-06-25 10:20:35 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GILD
2025-06-25 10:20:35 - src.data.alpaca_provider - WARNING - Insufficient data for TMUS: got 39, requested 50
2025-06-25 10:20:35 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:20:35] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:20:35 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:20:35] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:20:35 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:20:35] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:20:35 - src.data.alpaca_provider - WARNING - Insufficient data for TMUS: got 1, requested 50
2025-06-25 10:20:35 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:20:35] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:20:35 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TMUS
2025-06-25 10:20:35 - src.data.alpaca_provider - WARNING - Insufficient data for COP: got 4, requested 50
2025-06-25 10:20:35 - src.data.alpaca_provider - WARNING - Insufficient data for QCOM: got 6, requested 50
2025-06-25 10:20:35 - src.data.alpaca_provider - WARNING - Insufficient data for WELL: got 28, requested 50
2025-06-25 10:20:35 - src.data.alpaca_provider - WARNING - No data returned for WELL 5Min
2025-06-25 10:20:35 - src.data.alpaca_provider - WARNING - Insufficient data for WELL: got 30, requested 50
2025-06-25 10:20:35 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for WELL
2025-06-25 10:20:36 - src.data.alpaca_provider - WARNING - Insufficient data for CVX: got 1, requested 50
2025-06-25 10:20:36 - src.data.alpaca_provider - WARNING - Insufficient data for DHR: got 31, requested 50
2025-06-25 10:20:36 - src.data.alpaca_provider - WARNING - Insufficient data for DHR: got 1, requested 50
2025-06-25 10:20:36 - src.data.alpaca_provider - WARNING - Insufficient data for DHR: got 35, requested 50
2025-06-25 10:20:36 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DHR
2025-06-25 10:20:36 - src.data.alpaca_provider - WARNING - Insufficient data for SCHW: got 37, requested 50
2025-06-25 10:20:36 - src.data.alpaca_provider - WARNING - Insufficient data for SCHW: got 1, requested 50
2025-06-25 10:20:36 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for SCHW
2025-06-25 10:20:36 - src.data.alpaca_provider - WARNING - Insufficient data for SBUX: got 1, requested 50
2025-06-25 10:20:36 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for SBUX
2025-06-25 10:20:36 - src.data.alpaca_provider - WARNING - Insufficient data for ABT: got 29, requested 50
2025-06-25 10:20:36 - src.data.alpaca_provider - WARNING - No data returned for ABT 5Min
2025-06-25 10:20:36 - src.data.alpaca_provider - WARNING - Insufficient data for ABT: got 35, requested 50
2025-06-25 10:20:36 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABT
2025-06-25 10:20:36 - src.data.alpaca_provider - WARNING - Insufficient data for DIS: got 1, requested 50
2025-06-25 10:20:37 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DIS
2025-06-25 10:20:37 - src.data.alpaca_provider - WARNING - No data returned for ACN 5Min
2025-06-25 10:20:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:20:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:20:37 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ACN
2025-06-25 10:20:37 - src.data.alpaca_provider - WARNING - No data returned for AMGN 5Min
2025-06-25 10:20:37 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMGN
2025-06-25 10:20:37 - src.data.alpaca_provider - WARNING - Insufficient data for BA: got 2, requested 50
2025-06-25 10:20:37 - src.data.alpaca_provider - WARNING - Insufficient data for C: got 2, requested 50
2025-06-25 10:20:37 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for C
2025-06-25 10:20:38 - src.data.alpaca_provider - WARNING - Insufficient data for ETN: got 29, requested 50
2025-06-25 10:20:38 - src.data.alpaca_provider - WARNING - No data returned for ETN 5Min
2025-06-25 10:20:38 - src.data.alpaca_provider - WARNING - Insufficient data for ETN: got 33, requested 50
2025-06-25 10:20:38 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ETN
2025-06-25 10:20:38 - src.data.alpaca_provider - WARNING - Insufficient data for TXN: got 35, requested 50
2025-06-25 10:20:38 - src.data.alpaca_provider - WARNING - Insufficient data for TXN: got 1, requested 50
2025-06-25 10:20:38 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TXN
2025-06-25 10:20:38 - src.data.alpaca_provider - WARNING - Insufficient data for PANW: got 1, requested 50
2025-06-25 10:20:38 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PANW
2025-06-25 10:20:38 - src.data.alpaca_provider - WARNING - Insufficient data for ADI: got 30, requested 50
2025-06-25 10:20:38 - src.data.alpaca_provider - WARNING - No data returned for ADI 5Min
2025-06-25 10:20:38 - src.data.alpaca_provider - WARNING - Insufficient data for ADI: got 34, requested 50
2025-06-25 10:20:38 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADI
2025-06-25 10:20:38 - src.data.alpaca_provider - WARNING - Insufficient data for CRWD: got 3, requested 50
2025-06-25 10:20:39 - src.data.alpaca_provider - WARNING - Insufficient data for LRCX: got 2, requested 50
2025-06-25 10:20:39 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LRCX
2025-06-25 10:20:39 - src.data.alpaca_provider - WARNING - Insufficient data for KKR: got 29, requested 50
2025-06-25 10:20:39 - src.data.alpaca_provider - WARNING - No data returned for KKR 5Min
2025-06-25 10:20:39 - src.data.alpaca_provider - WARNING - Insufficient data for KKR: got 35, requested 50
2025-06-25 10:20:39 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for KKR
2025-06-25 10:20:39 - src.data.alpaca_provider - WARNING - Insufficient data for ADBE: got 1, requested 50
2025-06-25 10:20:39 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADBE
2025-06-25 10:20:39 - src.data.alpaca_provider - WARNING - Insufficient data for COF: got 36, requested 50
2025-06-25 10:20:39 - src.data.alpaca_provider - WARNING - No data returned for COF 5Min
2025-06-25 10:20:39 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for COF
2025-06-25 10:20:40 - src.data.alpaca_provider - WARNING - Insufficient data for APH: got 36, requested 50
2025-06-25 10:20:40 - src.data.alpaca_provider - WARNING - No data returned for APH 5Min
2025-06-25 10:20:40 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for APH
2025-06-25 10:20:40 - src.data.alpaca_provider - WARNING - Insufficient data for AMAT: got 3, requested 50
2025-06-25 10:20:40 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMAT
2025-06-25 10:20:40 - src.data.alpaca_provider - WARNING - Insufficient data for TJX: got 34, requested 50
2025-06-25 10:20:40 - src.data.alpaca_provider - WARNING - No data returned for TJX 5Min
2025-06-25 10:20:40 - src.data.alpaca_provider - WARNING - Insufficient data for TJX: got 38, requested 50
2025-06-25 10:20:40 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TJX
2025-06-25 10:20:41 - src.data.alpaca_provider - WARNING - Insufficient data for MS: got 39, requested 50
2025-06-25 10:20:41 - src.data.alpaca_provider - WARNING - No data returned for MS 5Min
2025-06-25 10:20:41 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MS
2025-06-25 10:20:41 - src.data.alpaca_provider - WARNING - Insufficient data for PGR: got 32, requested 50
2025-06-25 10:20:41 - src.data.alpaca_provider - WARNING - No data returned for PGR 5Min
2025-06-25 10:20:41 - src.data.alpaca_provider - WARNING - Insufficient data for PGR: got 35, requested 50
2025-06-25 10:20:41 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PGR
2025-06-25 10:20:41 - src.data.alpaca_provider - WARNING - Insufficient data for IBM: got 5, requested 50
2025-06-25 10:20:41 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for IBM
2025-06-25 10:20:41 - src.data.alpaca_provider - WARNING - Insufficient data for MCD: got 38, requested 50
2025-06-25 10:20:41 - src.data.alpaca_provider - WARNING - Insufficient data for MCD: got 1, requested 50
2025-06-25 10:20:41 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MCD
2025-06-25 10:20:41 - src.data.alpaca_provider - WARNING - No data returned for LLY 5Min
2025-06-25 10:20:42 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LLY
2025-06-25 10:20:42 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:20:42] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:20:42 - src.data.alpaca_provider - WARNING - Insufficient data for ADP: got 27, requested 50
2025-06-25 10:20:42 - src.data.alpaca_provider - WARNING - No data returned for ADP 5Min
2025-06-25 10:20:42 - src.data.alpaca_provider - WARNING - Insufficient data for ADP: got 31, requested 50
2025-06-25 10:20:42 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADP
2025-06-25 10:20:42 - src.data.alpaca_provider - WARNING - No data returned for MA 5Min
2025-06-25 10:20:42 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MA
2025-06-25 10:20:42 - src.data.alpaca_provider - WARNING - Insufficient data for ASML: got 8, requested 50
2025-06-25 10:20:42 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ASML
2025-06-25 10:20:42 - src.data.alpaca_provider - WARNING - No data returned for GEV 5Min
2025-06-25 10:20:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GEV
2025-06-25 10:20:43 - src.scanner.real_time_scanner - INFO - Scan cycle completed in 17.66 seconds
2025-06-25 10:20:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:20:52] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:20:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:20:52] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:20:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:20:52] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:20:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:20:52] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:21:02 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:21:02] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:21:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:21:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:21:12 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:21:12] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:21:13 - src.scanner.real_time_scanner - INFO - Starting scan cycle...
2025-06-25 10:21:13 - src.data.alpaca_provider - WARNING - Insufficient data for AAPL: got 26, requested 50
2025-06-25 10:21:13 - src.data.alpaca_provider - WARNING - Insufficient data for GOOGL: got 27, requested 50
2025-06-25 10:21:13 - src.data.alpaca_provider - WARNING - Insufficient data for GOOG: got 23, requested 50
2025-06-25 10:21:14 - src.data.alpaca_provider - WARNING - Insufficient data for AMZN: got 26, requested 50
2025-06-25 10:21:14 - src.data.alpaca_provider - WARNING - Insufficient data for AVGO: got 21, requested 50
2025-06-25 10:21:14 - src.data.alpaca_provider - WARNING - Insufficient data for NVDA: got 29, requested 50
2025-06-25 10:21:14 - src.data.alpaca_provider - WARNING - Insufficient data for MSFT: got 15, requested 50
2025-06-25 10:21:14 - src.data.alpaca_provider - WARNING - Insufficient data for CMCSA: got 2, requested 50
2025-06-25 10:21:14 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CMCSA
2025-06-25 10:21:14 - src.data.alpaca_provider - WARNING - Insufficient data for BAC: got 3, requested 50
2025-06-25 10:21:15 - src.data.alpaca_provider - WARNING - Insufficient data for AMD: got 29, requested 50
2025-06-25 10:21:15 - src.data.alpaca_provider - WARNING - Insufficient data for JNJ: got 33, requested 50
2025-06-25 10:21:15 - src.data.alpaca_provider - WARNING - No data returned for JNJ 5Min
2025-06-25 10:21:16 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JNJ
2025-06-25 10:21:16 - src.data.alpaca_provider - WARNING - Insufficient data for KO: got 3, requested 50
2025-06-25 10:21:16 - src.data.alpaca_provider - WARNING - Insufficient data for ABBV: got 31, requested 50
2025-06-25 10:21:16 - src.data.alpaca_provider - WARNING - No data returned for ABBV 5Min
2025-06-25 10:21:16 - src.data.alpaca_provider - WARNING - Insufficient data for ABBV: got 36, requested 50
2025-06-25 10:21:16 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABBV
2025-06-25 10:21:16 - src.data.alpaca_provider - WARNING - Insufficient data for JPM: got 2, requested 50
2025-06-25 10:21:16 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JPM
2025-06-25 10:21:16 - src.data.alpaca_provider - WARNING - Insufficient data for MRK: got 2, requested 50
2025-06-25 10:21:17 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MRK
2025-06-25 10:21:17 - src.data.alpaca_provider - WARNING - Insufficient data for CSCO: got 6, requested 50
2025-06-25 10:21:17 - src.data.alpaca_provider - WARNING - Insufficient data for MU: got 29, requested 50
2025-06-25 10:21:17 - src.data.alpaca_provider - WARNING - Insufficient data for GE: got 38, requested 50
2025-06-25 10:21:17 - src.data.alpaca_provider - WARNING - No data returned for GE 5Min
2025-06-25 10:21:17 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GE
2025-06-25 10:21:17 - src.data.alpaca_provider - WARNING - No data returned for CRM 5Min
2025-06-25 10:21:18 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CRM
2025-06-25 10:21:18 - src.scanner.real_time_scanner - INFO - ALERT [2]: NVDA - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:21:18 - src.data.alpaca_provider - WARNING - Insufficient data for NEE: got 1, requested 50
2025-06-25 10:21:18 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for NEE
2025-06-25 10:21:18 - src.data.alpaca_provider - WARNING - Insufficient data for ANET: got 8, requested 50
2025-06-25 10:21:18 - src.data.alpaca_provider - WARNING - Insufficient data for MDT: got 31, requested 50
2025-06-25 10:21:18 - src.data.alpaca_provider - WARNING - No data returned for MDT 5Min
2025-06-25 10:21:18 - src.data.alpaca_provider - WARNING - Insufficient data for MDT: got 38, requested 50
2025-06-25 10:21:18 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MDT
2025-06-25 10:21:18 - src.data.alpaca_provider - WARNING - Insufficient data for HD: got 2, requested 50
2025-06-25 10:21:18 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for HD
2025-06-25 10:21:19 - src.data.alpaca_provider - WARNING - Insufficient data for MO: got 6, requested 50
2025-06-25 10:21:19 - src.data.alpaca_provider - WARNING - Insufficient data for META: got 6, requested 50
2025-06-25 10:21:19 - src.data.alpaca_provider - WARNING - Insufficient data for BSX: got 32, requested 50
2025-06-25 10:21:19 - src.data.alpaca_provider - WARNING - Insufficient data for BSX: got 1, requested 50
2025-06-25 10:21:19 - src.data.alpaca_provider - WARNING - Insufficient data for BSX: got 36, requested 50
2025-06-25 10:21:19 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for BSX
2025-06-25 10:21:19 - src.data.alpaca_provider - WARNING - Insufficient data for GILD: got 37, requested 50
2025-06-25 10:21:19 - src.data.alpaca_provider - WARNING - No data returned for GILD 5Min
2025-06-25 10:21:19 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GILD
2025-06-25 10:21:19 - src.data.alpaca_provider - WARNING - Insufficient data for COP: got 4, requested 50
2025-06-25 10:21:20 - src.data.alpaca_provider - WARNING - Insufficient data for CVX: got 1, requested 50
2025-06-25 10:21:20 - src.data.alpaca_provider - WARNING - Insufficient data for DHR: got 31, requested 50
2025-06-25 10:21:20 - src.data.alpaca_provider - WARNING - Insufficient data for DHR: got 1, requested 50
2025-06-25 10:21:20 - src.data.alpaca_provider - WARNING - Insufficient data for DHR: got 35, requested 50
2025-06-25 10:21:20 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DHR
2025-06-25 10:21:20 - src.data.alpaca_provider - WARNING - Insufficient data for ABT: got 29, requested 50
2025-06-25 10:21:20 - src.data.alpaca_provider - WARNING - No data returned for ABT 5Min
2025-06-25 10:21:20 - src.data.alpaca_provider - WARNING - Insufficient data for ABT: got 35, requested 50
2025-06-25 10:21:20 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABT
2025-06-25 10:21:20 - src.data.alpaca_provider - WARNING - Insufficient data for DIS: got 1, requested 50
2025-06-25 10:21:20 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DIS
2025-06-25 10:21:20 - src.data.alpaca_provider - WARNING - No data returned for ACN 5Min
2025-06-25 10:21:21 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ACN
2025-06-25 10:21:21 - src.data.alpaca_provider - WARNING - No data returned for AMGN 5Min
2025-06-25 10:21:21 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMGN
2025-06-25 10:21:21 - src.data.alpaca_provider - WARNING - Insufficient data for BA: got 2, requested 50
2025-06-25 10:21:21 - src.data.alpaca_provider - WARNING - Insufficient data for C: got 2, requested 50
2025-06-25 10:21:21 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for C
2025-06-25 10:21:21 - src.data.alpaca_provider - WARNING - Insufficient data for ETN: got 29, requested 50
2025-06-25 10:21:21 - src.data.alpaca_provider - WARNING - No data returned for ETN 5Min
2025-06-25 10:21:21 - src.data.alpaca_provider - WARNING - Insufficient data for ETN: got 33, requested 50
2025-06-25 10:21:21 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ETN
2025-06-25 10:21:21 - src.data.alpaca_provider - WARNING - Insufficient data for ADI: got 30, requested 50
2025-06-25 10:21:21 - src.data.alpaca_provider - WARNING - No data returned for ADI 5Min
2025-06-25 10:21:22 - src.data.alpaca_provider - WARNING - Insufficient data for ADI: got 34, requested 50
2025-06-25 10:21:22 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADI
2025-06-25 10:21:22 - src.data.alpaca_provider - WARNING - Insufficient data for CRWD: got 3, requested 50
2025-06-25 10:21:22 - src.data.alpaca_provider - WARNING - Insufficient data for LRCX: got 2, requested 50
2025-06-25 10:21:22 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LRCX
2025-06-25 10:21:22 - src.data.alpaca_provider - WARNING - Insufficient data for KKR: got 29, requested 50
2025-06-25 10:21:22 - src.data.alpaca_provider - WARNING - No data returned for KKR 5Min
2025-06-25 10:21:22 - src.data.alpaca_provider - WARNING - Insufficient data for KKR: got 35, requested 50
2025-06-25 10:21:22 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for KKR
2025-06-25 10:21:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:21:22] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:21:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:21:22] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:21:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:21:22] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:21:22 - src.data.alpaca_provider - WARNING - Insufficient data for ADBE: got 1, requested 50
2025-06-25 10:21:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:21:22] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 10:21:22 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADBE
2025-06-25 10:21:22 - src.data.alpaca_provider - WARNING - Insufficient data for COF: got 36, requested 50
2025-06-25 10:21:22 - src.data.alpaca_provider - WARNING - No data returned for COF 5Min
2025-06-25 10:21:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for COF
2025-06-25 10:21:23 - src.data.alpaca_provider - WARNING - Insufficient data for APH: got 36, requested 50
2025-06-25 10:21:23 - src.data.alpaca_provider - WARNING - No data returned for APH 5Min
2025-06-25 10:21:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for APH
2025-06-25 10:21:23 - src.data.alpaca_provider - WARNING - Insufficient data for AMAT: got 3, requested 50
2025-06-25 10:21:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMAT
2025-06-25 10:21:23 - src.data.alpaca_provider - WARNING - Insufficient data for MS: got 39, requested 50
2025-06-25 10:21:23 - src.data.alpaca_provider - WARNING - No data returned for MS 5Min
2025-06-25 10:21:23 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MS
2025-06-25 10:21:23 - src.data.alpaca_provider - WARNING - Insufficient data for IBM: got 5, requested 50
2025-06-25 10:21:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for IBM
2025-06-25 10:21:24 - src.data.alpaca_provider - WARNING - Insufficient data for MCD: got 38, requested 50
2025-06-25 10:21:24 - src.data.alpaca_provider - WARNING - Insufficient data for MCD: got 1, requested 50
2025-06-25 10:21:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MCD
2025-06-25 10:21:24 - src.data.alpaca_provider - WARNING - No data returned for LLY 5Min
2025-06-25 10:21:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LLY
2025-06-25 10:21:24 - src.data.alpaca_provider - WARNING - Insufficient data for ADP: got 27, requested 50
2025-06-25 10:21:24 - src.data.alpaca_provider - WARNING - No data returned for ADP 5Min
2025-06-25 10:21:24 - src.data.alpaca_provider - WARNING - Insufficient data for ADP: got 31, requested 50
2025-06-25 10:21:24 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADP
2025-06-25 10:21:25 - src.data.alpaca_provider - WARNING - No data returned for MA 5Min
2025-06-25 10:21:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MA
2025-06-25 10:21:25 - src.data.alpaca_provider - WARNING - Insufficient data for ASML: got 8, requested 50
2025-06-25 10:21:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ASML
2025-06-25 10:21:25 - src.data.alpaca_provider - WARNING - No data returned for GEV 5Min
2025-06-25 10:21:25 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GEV
2025-06-25 10:21:25 - src.scanner.real_time_scanner - INFO - Scan cycle completed in 12.60 seconds
2025-06-25 10:21:32 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:21:32] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:21:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:21:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:21:42 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:21:42] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:21:43 - src.scanner.real_time_scanner - INFO - Starting scan cycle...
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CMCSA
2025-06-25 10:21:43 - src.scanner.real_time_scanner - INFO - ALERT [2]: NVDA - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:21:43 - src.scanner.real_time_scanner - INFO - ALERT [2]: PLTR - Potential squeeze release detected | Timeframes: 15Min
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JNJ
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABBV
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for JPM
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MRK
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for WFC
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GE
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for CRM
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PM
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PEP
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for NEE
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MDT
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for RTX
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for HD
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PG
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for BSX
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GILD
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TMUS
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for WELL
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DHR
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for SCHW
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for SBUX
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ABT
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for DIS
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ACN
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMGN
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for C
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ETN
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TXN
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PANW
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADI
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LRCX
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for KKR
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADBE
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for COF
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for APH
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for AMAT
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for TJX
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MS
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for PGR
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for IBM
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MCD
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for LLY
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ADP
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for MA
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for ASML
2025-06-25 10:21:43 - src.indicators.multi_timeframe - WARNING - Insufficient primary timeframe data for GEV
2025-06-25 10:21:43 - src.scanner.real_time_scanner - INFO - Scan cycle completed in 0.48 seconds
2025-06-25 10:21:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:21:52] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 10:21:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:21:52] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 10:21:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:21:52] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 10:21:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 10:21:52] "GET /api/trading/positions HTTP/1.1" 200 -
