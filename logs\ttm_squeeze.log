2025-06-25 08:35:43 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:35:44 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:44 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:35:45 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:35:45 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:35:45 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:45 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:35:45 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:35:45 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:35:45 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:35:45 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:45 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:35:45 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:35:45 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:35:45 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:35:45 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:35:45 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:45 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:35:46 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:35:46 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:35:46 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:35:46 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:35:46 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-25 08:35:46 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 08:35:46 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:35:47 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:35:47 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:47 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:35:47 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:35:47 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:35:47 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:47 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:35:47 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:35:47 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:35:47 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:35:47 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:47 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:35:47 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:35:47 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:35:47 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:35:47 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:35:48 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:35:48 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:35:48 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:35:48 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:35:48 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:35:48 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:35:48 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:35:48 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET / HTTP/1.1" 200 -
2025-06-25 08:36:09 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\unicode_escape.py', reloading
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-25 08:36:09 - src.trading.alpaca_trader - ERROR - Error getting positions: invalid literal for int() with base 10: '0.0009975'
2025-06-25 08:36:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:36:09 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:36:10 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:36:10 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:36:10 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:36:10 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:36:10 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:36:11 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:36:11 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:36:11 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:36:11 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:36:11 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:36:11 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:36:11 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:36:11 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:36:11 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:36:11 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:36:11 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:36:11 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:36:11 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:36:11 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:36:11 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:36:11 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:36:11 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:36:11 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:36:11 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:36:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:36:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:36:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:36:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:36:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:36:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:36:39 - src.trading.alpaca_trader - ERROR - Error getting positions: invalid literal for int() with base 10: '0.0009975'
2025-06-25 08:36:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:36:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:36:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:36:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:36:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:37:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:37:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:37:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:37:09 - src.trading.alpaca_trader - ERROR - Error getting positions: invalid literal for int() with base 10: '0.0009975'
2025-06-25 08:37:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:37:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:37:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:37:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:37:36 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 08:37:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:36] "POST /api/scanner/start HTTP/1.1" 200 -
2025-06-25 08:37:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:36] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:37:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:37:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:37:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:37:39 - src.trading.alpaca_trader - ERROR - Error getting positions: invalid literal for int() with base 10: '0.0009975'
2025-06-25 08:37:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:37:45 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\cli_test.py', reloading
2025-06-25 08:37:45 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\cli_test.py', reloading
2025-06-25 08:37:46 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:37:46 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:37:47 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:37:47 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:37:47 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:37:47 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:37:47 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:37:47 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:37:47 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:37:47 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:37:47 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:37:47 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:37:47 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:37:47 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:37:47 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:37:47 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:37:47 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:37:48 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:37:48 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:37:48 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:37:48 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:37:48 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:37:48 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:37:48 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:37:48 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:37:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:37:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:37:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:37:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:38:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:38:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:38:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:38:09 - src.trading.alpaca_trader - ERROR - Error getting positions: invalid literal for int() with base 10: '0.0009975'
2025-06-25 08:38:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:38:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:38:21 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 08:38:21 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:21] "POST /api/scanner/start HTTP/1.1" 200 -
2025-06-25 08:38:21 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:21] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:38:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:38:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:38:29 - src.scanner.stock_screener - INFO - Refreshing stock universe...
2025-06-25 08:38:29 - src.data.fmp_provider - INFO - Retrieved 503 S&P 500 symbols
2025-06-25 08:38:29 - src.data.data_manager - INFO - Added 503 S&P 500 stocks to universe
2025-06-25 08:38:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:38:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:38:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:38:39 - src.trading.alpaca_trader - ERROR - Error getting positions: invalid literal for int() with base 10: '0.0009975'
2025-06-25 08:38:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:38:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:38:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:38:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:38:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:39:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:39:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:39:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:39:09 - src.trading.alpaca_trader - ERROR - Error getting positions: invalid literal for int() with base 10: '0.0009975'
2025-06-25 08:39:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:39:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:39:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:39:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:39:35 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\alpaca_trader.py', reloading
2025-06-25 08:39:35 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\alpaca_trader.py', reloading
2025-06-25 08:39:35 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:39:36 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:39:36 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:36 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:39:36 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:39:36 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:39:36 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:36 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:39:37 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:39:37 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:39:37 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:39:37 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:37 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:39:37 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:39:37 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:39:37 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:39:37 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:39:37 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:37 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:39:37 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:39:37 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:39:37 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:39:37 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:39:37 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:39:37 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:39:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:39:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:39:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:39:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:39:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:39:51 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\alpaca_trader.py', reloading
2025-06-25 08:39:51 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\alpaca_trader.py', reloading
2025-06-25 08:39:51 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:39:52 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:39:52 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:52 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:39:53 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:39:53 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:39:53 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:53 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:39:53 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:39:53 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:39:53 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:39:53 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:53 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:39:53 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:39:53 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:39:53 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:39:53 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:39:53 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:39:53 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:39:53 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:39:53 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:39:53 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:39:53 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:39:53 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:39:53 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:39:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:39:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:39:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:40:06 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\alpaca_trader.py', reloading
2025-06-25 08:40:06 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\alpaca_trader.py', reloading
2025-06-25 08:40:07 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:40:08 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:40:08 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:08 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:40:08 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:40:08 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:40:08 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:08 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:40:08 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:40:08 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:40:08 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:40:08 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:08 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:40:08 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:40:08 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:40:08 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:40:08 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:40:09 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:09 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:40:09 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:40:09 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:40:09 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:40:09 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:40:09 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:40:09 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:40:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:40:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:40:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:40:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:40:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:40:19 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\risk_manager.py', reloading
2025-06-25 08:40:19 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\trading\\risk_manager.py', reloading
2025-06-25 08:40:20 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:40:21 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:40:21 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:21 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:40:21 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:40:21 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:40:21 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:21 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:40:22 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:40:22 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:40:22 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:40:22 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:22 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:40:22 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:40:22 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:40:22 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:40:22 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:40:22 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:40:22 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:40:22 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:40:22 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:40:22 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:40:22 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:40:22 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:40:22 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:40:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:40:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:40:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:40:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:40:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:40:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:40:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:40:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:40:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:40:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:41:04 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\test_fractional_fix.py', reloading
2025-06-25 08:41:04 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\test_fractional_fix.py', reloading
2025-06-25 08:41:05 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 08:41:06 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 08:41:08 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:41:08 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:41:08 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:41:08 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:41:08 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:41:08 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:41:08 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:41:08 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:41:08 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:41:08 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:41:08 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 08:41:08 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 08:41:08 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 08:41:08 - src.ui.app - INFO - Trading components initialized
2025-06-25 08:41:08 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 08:41:09 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 08:41:09 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 08:41:09 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 08:41:09 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 08:41:09 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 08:41:09 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 08:41:09 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 08:41:09 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 08:41:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:41:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:41:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:41:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:41:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:41:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:41:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:41:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:41:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:41:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:41:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:41:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:41:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:41:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:41:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:42:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:42:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:42:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:42:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:42:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:42:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:42:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:42:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:42:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:42:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:42:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:42:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:42:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:42:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:42:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:43:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:43:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:43:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:43:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:43:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:43:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:43:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:43:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:43:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:43:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:43:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:43:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:43:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:43:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:43:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:44:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:44:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:44:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:44:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:44:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:44:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:44:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:44:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:44:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:44:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:44:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:44:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:44:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:44:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:44:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:45:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:45:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:45:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:45:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:45:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:45:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:45:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:45:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:45:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:45:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:45:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:45:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:45:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:45:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:45:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:46:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:46:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:46:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:46:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:46:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:46:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:46:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:46:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:46:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:46:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:46:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:46:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:46:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:46:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:46:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:47:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:47:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:47:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:47:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:47:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:47:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:47:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:47:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:47:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:47:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:47:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:47:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:47:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:47:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:47:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:48:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:48:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:48:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:48:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:48:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:48:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:48:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:48:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:48:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:48:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:48:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:48:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:48:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:48:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:48:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:49:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:49:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:49:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:49:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:49:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:49:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:49:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:49:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:49:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:49:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:49:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:49:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:49:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:49:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:49:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:50:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:50:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:50:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:50:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:50:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:50:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:50:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:50:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:50:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:50:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:50:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:50:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:50:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:50:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:50:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:51:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:51:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:51:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:51:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:51:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:51:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:51:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:51:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:51:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:51:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:51:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:51:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:51:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:51:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:51:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:52:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:52:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:52:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:52:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:52:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:52:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:52:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:52:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:52:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:52:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:52:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:52:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:52:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:52:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:52:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:53:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:53:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:53:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:53:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:53:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:53:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:53:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:53:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:53:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:53:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:53:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:53:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:53:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:53:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:53:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:54:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:54:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:54:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:54:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:54:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:54:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:54:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:54:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:54:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:54:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:54:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:54:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:54:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:54:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:54:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:55:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:55:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:55:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:55:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:55:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:55:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:55:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:55:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:55:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:55:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:55:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:55:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:55:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:55:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:55:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:56:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:56:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:56:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:56:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:56:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:56:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:56:29 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:29] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:56:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:56:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:56:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:56:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:56:49 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:49] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:56:53 - src.scanner.stock_screener - INFO - Refreshing stock universe...
2025-06-25 08:56:53 - src.data.fmp_provider - INFO - Retrieved 503 S&P 500 symbols
2025-06-25 08:56:53 - src.data.data_manager - INFO - Added 503 S&P 500 stocks to universe
2025-06-25 08:56:54 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:54] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:56:59 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:56:59] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:57:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:57:09] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 08:57:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:57:09] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:57:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:57:09] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 08:57:09 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:57:09] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 08:57:19 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:57:19] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 08:57:24 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 08:57:24] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:02:16 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:02:16 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:02:16 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:02:16 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:02:16 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-24T08:02:16.760799' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T08:02:16.760799": extra text: "T08:02:16.760799"
2025-06-25 09:02:17 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-25T00:42:17.345972' as RFC3339 or 2006-01-02 time: parsing time "2025-06-25T00:42:17.345972": extra text: "T00:42:17.345972"
2025-06-25 09:02:17 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-23T07:02:17.471312' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:17.471312": extra text: "T07:02:17.471312"
2025-06-25 09:02:17 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-24T08:02:17.619130' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T08:02:17.619130": extra text: "T08:02:17.619130"
2025-06-25 09:02:17 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-25T00:42:17.747286' as RFC3339 or 2006-01-02 time: parsing time "2025-06-25T00:42:17.747286": extra text: "T00:42:17.747286"
2025-06-25 09:02:17 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-23T07:02:17.871360' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:17.871360": extra text: "T07:02:17.871360"
2025-06-25 09:02:18 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-24T08:02:18.023134' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T08:02:18.023134": extra text: "T08:02:18.023134"
2025-06-25 09:02:18 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-25T00:42:18.443080' as RFC3339 or 2006-01-02 time: parsing time "2025-06-25T00:42:18.443080": extra text: "T00:42:18.443080"
2025-06-25 09:02:18 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-23T07:02:18.556751' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:18.556751": extra text: "T07:02:18.556751"
2025-06-25 09:02:18 - src.data.alpaca_provider - ERROR - Error getting historical data for SPY: Invalid format for parameter start: error parsing '2025-06-24T08:02:18.902692' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T08:02:18.902692": extra text: "T08:02:18.902692"
2025-06-25 09:02:19 - src.data.alpaca_provider - ERROR - Error getting historical data for SPY: Invalid format for parameter start: error parsing '2025-06-25T00:42:19.015290' as RFC3339 or 2006-01-02 time: parsing time "2025-06-25T00:42:19.015290": extra text: "T00:42:19.015290"
2025-06-25 09:02:19 - src.data.alpaca_provider - ERROR - Error getting historical data for SPY: Invalid format for parameter start: error parsing '2025-06-23T07:02:19.241807' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:19.241807": extra text: "T07:02:19.241807"
2025-06-25 09:02:19 - src.data.alpaca_provider - ERROR - Error getting historical data for QQQ: Invalid format for parameter start: error parsing '2025-06-24T08:02:19.443166' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T08:02:19.443166": extra text: "T08:02:19.443166"
2025-06-25 09:02:19 - src.data.alpaca_provider - ERROR - Error getting historical data for QQQ: Invalid format for parameter start: error parsing '2025-06-25T00:42:19.556823' as RFC3339 or 2006-01-02 time: parsing time "2025-06-25T00:42:19.556823": extra text: "T00:42:19.556823"
2025-06-25 09:02:19 - src.data.alpaca_provider - ERROR - Error getting historical data for QQQ: Invalid format for parameter start: error parsing '2025-06-23T07:02:19.823436' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:19.823436": extra text: "T07:02:19.823436"
2025-06-25 09:02:20 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:02:20 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:02:20 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:02:20 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:02:20 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-23T07:02:20.545811' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:20.545811": extra text: "T07:02:20.545811"
2025-06-25 09:02:20 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-24T16:22:20.836117' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T16:22:20.836117": extra text: "T16:22:20.836117"
2025-06-25 09:02:21 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-21T05:02:21.111701' as RFC3339 or 2006-01-02 time: parsing time "2025-06-21T05:02:21.111701": extra text: "T05:02:21.111701"
2025-06-25 09:02:21 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-23T07:02:21.270553' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:21.270553": extra text: "T07:02:21.270553"
2025-06-25 09:02:21 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-24T16:22:21.707704' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T16:22:21.707704": extra text: "T16:22:21.707704"
2025-06-25 09:02:21 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-21T05:02:21.837304' as RFC3339 or 2006-01-02 time: parsing time "2025-06-21T05:02:21.837304": extra text: "T05:02:21.837304"
2025-06-25 09:02:22 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-23T07:02:21.985817' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:21.985817": extra text: "T07:02:21.985817"
2025-06-25 09:02:22 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-24T16:22:22.123730' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T16:22:22.123730": extra text: "T16:22:22.123730"
2025-06-25 09:02:22 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-21T05:02:22.249935' as RFC3339 or 2006-01-02 time: parsing time "2025-06-21T05:02:22.249935": extra text: "T05:02:22.249935"
2025-06-25 09:02:22 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:02:22 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:02:22 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:02:22 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:02:22 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:02:22 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-23T07:02:22.809924' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:22.809924": extra text: "T07:02:22.809924"
2025-06-25 09:02:23 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-24T16:22:23.095599' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T16:22:23.095599": extra text: "T16:22:23.095599"
2025-06-25 09:02:24 - src.data.alpaca_provider - ERROR - Error getting historical data for AAPL: Invalid format for parameter start: error parsing '2025-06-21T05:02:24.250590' as RFC3339 or 2006-01-02 time: parsing time "2025-06-21T05:02:24.250590": extra text: "T05:02:24.250590"
2025-06-25 09:02:24 - src.scanner.real_time_scanner - INFO - Manual scan result for AAPL: avoid
2025-06-25 09:02:24 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-23T07:02:24.409218' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:24.409218": extra text: "T07:02:24.409218"
2025-06-25 09:02:24 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-24T16:22:24.542535' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T16:22:24.542535": extra text: "T16:22:24.542535"
2025-06-25 09:02:24 - src.data.alpaca_provider - ERROR - Error getting historical data for MSFT: Invalid format for parameter start: error parsing '2025-06-21T05:02:24.671077' as RFC3339 or 2006-01-02 time: parsing time "2025-06-21T05:02:24.671077": extra text: "T05:02:24.671077"
2025-06-25 09:02:24 - src.scanner.real_time_scanner - INFO - Manual scan result for MSFT: hold
2025-06-25 09:02:24 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-23T07:02:24.820633' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:24.820633": extra text: "T07:02:24.820633"
2025-06-25 09:02:25 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-24T16:22:24.959097' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T16:22:24.959097": extra text: "T16:22:24.959097"
2025-06-25 09:02:25 - src.data.alpaca_provider - ERROR - Error getting historical data for TSLA: Invalid format for parameter start: error parsing '2025-06-21T05:02:25.084204' as RFC3339 or 2006-01-02 time: parsing time "2025-06-21T05:02:25.084204": extra text: "T05:02:25.084204"
2025-06-25 09:02:26 - src.scanner.real_time_scanner - INFO - Manual scan result for TSLA: avoid
2025-06-25 09:02:26 - src.data.alpaca_provider - ERROR - Error getting historical data for SPY: Invalid format for parameter start: error parsing '2025-06-23T07:02:26.434174' as RFC3339 or 2006-01-02 time: parsing time "2025-06-23T07:02:26.434174": extra text: "T07:02:26.434174"
2025-06-25 09:02:26 - src.data.alpaca_provider - ERROR - Error getting historical data for SPY: Invalid format for parameter start: error parsing '2025-06-24T16:22:26.547501' as RFC3339 or 2006-01-02 time: parsing time "2025-06-24T16:22:26.547501": extra text: "T16:22:26.547501"
2025-06-25 09:02:26 - src.data.alpaca_provider - ERROR - Error getting historical data for SPY: Invalid format for parameter start: error parsing '2025-06-21T05:02:26.678284' as RFC3339 or 2006-01-02 time: parsing time "2025-06-21T05:02:26.678284": extra text: "T05:02:26.678284"
2025-06-25 09:02:26 - src.scanner.real_time_scanner - INFO - Manual scan result for SPY: hold
2025-06-25 09:02:26 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:02:26 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:02:27 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:02:27 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:02:27 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:02:27 - src.scanner.real_time_scanner - INFO - Refreshing scan universe...
2025-06-25 09:02:27 - src.scanner.stock_screener - INFO - Refreshing stock universe...
2025-06-25 09:02:28 - src.data.fmp_provider - INFO - Retrieved 503 S&P 500 symbols
2025-06-25 09:02:28 - src.data.data_manager - INFO - Added 503 S&P 500 stocks to universe
2025-06-25 09:03:29 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:03:29 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:29 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:03:29 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:03:29 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:03:29 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:29 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:03:29 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:03:29 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:03:29 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:03:30 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:30 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:03:30 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:03:30 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:03:30 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:03:30 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:03:30 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:30 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:03:30 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:03:30 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:03:30 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:03:30 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:03:30 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:03:30 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:03:30 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-25 09:03:30 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 09:03:30 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:03:31 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:03:31 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:31 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:03:31 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:03:31 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:03:31 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:31 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:03:31 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:03:31 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:03:31 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:03:32 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:32 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:03:32 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:03:32 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:03:32 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:03:32 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:03:32 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:03:32 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:03:32 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:03:32 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:03:32 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:03:32 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:03:32 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:03:32 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:03:32 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:03:32 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:05:19 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\data\\alpaca_provider.py', reloading
2025-06-25 09:05:19 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\data\\alpaca_provider.py', reloading
2025-06-25 09:05:20 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:05:21 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:05:21 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:21 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:21 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:21 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:21 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:21 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:21 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:21 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:21 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:05:22 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:22 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:05:22 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:05:22 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:05:22 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:05:22 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:05:22 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:22 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:22 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:22 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:22 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:05:22 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:05:22 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:05:22 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:05:22 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:05:22 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:05:29 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\data\\alpaca_provider.py', reloading
2025-06-25 09:05:29 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\data\\alpaca_provider.py', reloading
2025-06-25 09:05:29 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:05:30 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:05:30 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:30 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:30 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:30 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:31 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:31 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:31 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:31 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:31 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:05:31 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:31 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:05:31 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:05:31 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:05:31 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:05:31 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:05:31 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:31 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:31 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:31 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:31 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:05:31 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:05:31 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:05:31 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:05:31 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:05:31 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:05:41 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\data\\alpaca_provider.py', reloading
2025-06-25 09:05:41 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\src\\data\\alpaca_provider.py', reloading
2025-06-25 09:05:42 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:05:42 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:05:43 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:43 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:43 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:43 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:43 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:43 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:43 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:43 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:43 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:05:43 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:43 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:05:43 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:05:43 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:05:43 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:05:43 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:05:43 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:43 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:44 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:44 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:44 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:05:44 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:05:44 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:05:44 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:05:44 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:05:44 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:05:58 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:05:58 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:58 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:59 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:59 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:59 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:59 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:05:59 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:05:59 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:05:59 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:05:59 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:59 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:05:59 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:05:59 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:05:59 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:05:59 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:05:59 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:05:59 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:06:00 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:06:00 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:06:00 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:06:00 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:06:00 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:06:00 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:06:00 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-25 09:06:00 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-25 09:06:00 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:06:00 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:06:01 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:06:01 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:06:01 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:06:01 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:06:01 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:06:01 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:06:01 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:06:01 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:06:01 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:06:01 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:06:01 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:06:01 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:06:01 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:06:01 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:06:01 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:06:02 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:06:02 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:06:02 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:06:02 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:06:02 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:06:02 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:06:02 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:06:02 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:06:02 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:06:02 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:08:20 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\test_data_fix.py', reloading
2025-06-25 09:08:20 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Desktop\\TTMFINAL\\test_data_fix.py', reloading
2025-06-25 09:08:20 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-25 09:08:21 - __main__ - INFO - Starting TTM Squeeze Trading System
2025-06-25 09:08:26 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:08:26 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:08:26 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:08:26 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:08:26 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:08:26 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:08:26 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:08:26 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:08:26 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:08:27 - src.trading.alpaca_trader - INFO - Connected to Alpaca - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:08:27 - src.trading.alpaca_trader - INFO - Account Status: ACTIVE
2025-06-25 09:08:27 - src.trading.alpaca_trader - INFO - Buying Power: $59,790.98
2025-06-25 09:08:27 - src.trading.risk_manager - INFO - Risk manager initialized
2025-06-25 09:08:27 - src.ui.app - INFO - Trading components initialized
2025-06-25 09:08:27 - src.ui.app - INFO - Application components initialized successfully
2025-06-25 09:08:27 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:08:27 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:08:27 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:08:27 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:08:27 - src.scanner.real_time_scanner - INFO - Real-time scanner initialized
2025-06-25 09:08:27 - __main__ - INFO - Starting real-time scanner...
2025-06-25 09:08:27 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:08:27 - __main__ - INFO - Starting web interface on 127.0.0.1:5000
2025-06-25 09:08:27 - werkzeug - WARNING -  * Debugger is active!
2025-06-25 09:08:27 - werkzeug - INFO -  * Debugger PIN: 115-262-351
2025-06-25 09:08:28 - src.data.alpaca_provider - INFO - Connected to Alpaca API - Account: 226af6b8-4bee-4753-b136-817491e10ee2
2025-06-25 09:08:28 - src.data.data_manager - INFO - Alpaca provider initialized
2025-06-25 09:08:29 - src.data.fmp_provider - INFO - Connected to Financial Modeling Prep API
2025-06-25 09:08:29 - src.data.data_manager - INFO - FMP provider initialized
2025-06-25 09:08:29 - src.data.alpaca_provider - WARNING - Insufficient data for AAPL: got 13, requested 50
2025-06-25 09:08:29 - src.data.alpaca_provider - WARNING - Insufficient data for MSFT: got 7, requested 50
2025-06-25 09:08:29 - src.data.alpaca_provider - WARNING - Insufficient data for TSLA: got 14, requested 50
2025-06-25 09:08:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:36] "GET / HTTP/1.1" 200 -
2025-06-25 09:08:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:36] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-25 09:08:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-25 09:08:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:36] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:08:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:36] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:08:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:37] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:08:46 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:46] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:08:51 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:51] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:08:56 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:08:56] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:09:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:06] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:09:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:06] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:09:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:06] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:09:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:07] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:09:16 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:16] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:09:21 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:21] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:09:26 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:26] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:09:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:36] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:09:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:36] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:09:36 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:36] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:09:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:37] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:09:46 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:46] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:09:51 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:51] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:09:56 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:09:56] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:10:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:06] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:10:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:06] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:10:06 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:06] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:10:07 - src.scanner.real_time_scanner - INFO - Real-time scanner started
2025-06-25 09:10:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:07] "POST /api/scanner/start HTTP/1.1" 200 -
2025-06-25 09:10:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:10:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:07] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:10:16 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:16] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:10:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:22] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:10:27 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:27] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:10:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:37] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:10:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:10:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:37] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:10:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:37] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:10:47 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:47] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:10:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:52] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:10:57 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:10:57] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:11:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:07] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:11:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:11:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:07] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:11:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:07] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:11:17 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:17] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:11:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:37] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:11:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:37] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:11:38 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:38] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:11:38 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:11:38] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:12:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:12:39] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:12:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:12:39] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:12:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:12:39] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:12:39 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:12:39] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:13:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:13:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:13:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:13:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:13:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:13:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:13:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:13:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:14:27 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:27] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:14:27 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:27] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:14:27 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:27] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:14:27 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:27] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:14:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:37] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:14:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:37] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:14:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:37] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:14:37 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:37] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:14:47 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:47] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:14:52 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:52] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:14:57 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:14:57] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:15:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:07] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:15:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:07] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:15:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:07] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:15:07 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:07] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:15:17 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:17] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:15:22 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:22] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:15:27 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:27] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:15:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:15:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:15:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:15:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:15:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:16:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:16:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:16:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:16:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:16:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:16:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:16:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:16:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:17:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:17:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:17:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:17:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:17:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:17:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:17:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:17:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:18:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:18:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:18:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:18:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:18:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:18:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:18:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:18:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:19:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:19:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:19:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:19:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:19:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:19:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:19:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:19:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:20:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:20:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:20:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:20:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:20:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:20:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:20:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:20:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:21:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:21:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:21:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:21:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:21:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:21:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:21:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:21:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:22:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:22:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:22:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:22:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:22:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:22:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:22:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:22:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:23:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:23:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:23:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:23:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:23:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:23:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:23:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:23:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:24:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:24:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:24:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:24:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:24:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:24:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:24:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:24:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:25:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:25:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:25:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:25:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:25:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:25:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:25:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:25:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:26:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:26:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:26:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:26:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:26:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:26:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:26:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:26:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:27:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:27:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:27:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:27:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:27:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:27:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:27:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:27:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:28:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:28:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:28:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:28:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:28:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:28:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:28:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:28:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:29:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:29:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:29:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:29:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:29:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:29:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:29:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:29:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:30:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:30:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:30:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:30:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:30:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:30:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:30:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:30:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:31:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:31:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:31:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:31:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:31:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:31:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:31:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:31:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:32:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:32:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:32:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:32:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:32:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:32:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:32:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:32:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:33:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:33:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:33:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:33:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:33:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:33:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:33:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:33:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:34:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:34:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:34:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:34:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:34:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:34:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:34:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:34:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:35:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:35:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:35:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:35:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:35:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:35:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:35:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:35:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:36:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:36:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:36:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:36:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:36:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:36:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:36:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:36:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:37:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:37:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:37:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:37:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:37:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:37:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:37:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:37:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:38:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:38:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:38:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:38:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:38:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:38:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:38:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:38:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:39:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:39:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:39:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:39:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:39:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:39:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:39:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:39:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:40:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:40:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:40:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:40:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:40:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:40:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:40:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:40:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:41:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:41:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:41:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:41:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:41:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:41:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:41:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:41:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:42:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:42:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:42:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:42:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:42:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:42:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:42:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:42:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:43:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:43:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:43:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:43:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:43:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:43:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:43:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:43:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:44:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:44:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:44:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:44:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:44:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:44:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:44:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:44:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:45:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:45:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:45:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:45:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:45:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:45:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:45:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:45:40] "GET /api/trading/positions HTTP/1.1" 200 -
2025-06-25 09:46:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:46:40] "GET /api/scanner/alerts?limit=50 HTTP/1.1" 200 -
2025-06-25 09:46:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:46:40] "GET /api/scanner/status HTTP/1.1" 200 -
2025-06-25 09:46:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:46:40] "GET /api/trading/account HTTP/1.1" 200 -
2025-06-25 09:46:40 - werkzeug - INFO - 127.0.0.1 - - [25/Jun/2025 09:46:40] "GET /api/trading/positions HTTP/1.1" 200 -
