"""
Financial Modeling Prep API Provider for TTM Squeeze Trading System
"""
import pandas as pd
import requests
from typing import Optional, Dict, List
import logging
from datetime import datetime, timedelta
import time

from config import Config

logger = logging.getLogger(__name__)

class FMPDataProvider:
    """Financial Modeling Prep API data provider"""
    
    def __init__(self):
        """Initialize FMP API connection"""
        self.api_key = Config.FMP_API_KEY
        self.base_url = "https://financialmodelingprep.com/api/v3"
        self.session = requests.Session()
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.1  # 10 requests per second max
        
        # Test connection
        try:
            self._test_connection()
            logger.info("Connected to Financial Modeling Prep API")
        except Exception as e:
            logger.error(f"Failed to connect to FMP API: {e}")
    
    def _test_connection(self):
        """Test API connection"""
        url = f"{self.base_url}/profile/AAPL"
        params = {'apikey': self.api_key}
        
        response = self.session.get(url, params=params, timeout=10)
        response.raise_for_status()
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last)
        
        self.last_request_time = time.time()
    
    def _make_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """Make API request with rate limiting and error handling"""
        try:
            self._rate_limit()
            
            url = f"{self.base_url}/{endpoint}"
            request_params = {'apikey': self.api_key}
            
            if params:
                request_params.update(params)
            
            response = self.session.get(url, params=request_params, timeout=30)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed for {endpoint}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in API request: {e}")
            return None
    
    def get_sp500_list(self) -> List[str]:
        """Get list of S&P 500 stocks"""
        try:
            data = self._make_request("sp500_constituent")
            
            if data is None:
                logger.error("Failed to get S&P 500 list")
                return []
            
            symbols = [item['symbol'] for item in data if 'symbol' in item]
            logger.info(f"Retrieved {len(symbols)} S&P 500 symbols")
            
            return symbols
            
        except Exception as e:
            logger.error(f"Error getting S&P 500 list: {e}")
            return []
    
    def get_company_profile(self, symbol: str) -> Optional[Dict]:
        """Get company profile information"""
        try:
            data = self._make_request(f"profile/{symbol}")
            
            if data and len(data) > 0:
                return data[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting company profile for {symbol}: {e}")
            return None
    
    def get_market_cap_filter(self, min_market_cap: float) -> List[str]:
        """Get stocks with market cap above threshold"""
        try:
            # Get all available stocks
            data = self._make_request("stock/list")
            
            if data is None:
                return []
            
            filtered_symbols = []
            
            for stock in data:
                symbol = stock.get('symbol', '')
                
                # Skip if no symbol
                if not symbol:
                    continue
                
                # Get market cap
                profile = self.get_company_profile(symbol)
                if profile:
                    market_cap = profile.get('mktCap', 0)
                    if market_cap >= min_market_cap:
                        filtered_symbols.append(symbol)
                
                # Limit to prevent too many API calls
                if len(filtered_symbols) >= 1000:
                    break
            
            logger.info(f"Found {len(filtered_symbols)} stocks with market cap >= ${min_market_cap:,.0f}")
            return filtered_symbols
            
        except Exception as e:
            logger.error(f"Error filtering by market cap: {e}")
            return []
    
    def get_historical_data(self, symbol: str, timeframe: str, 
                          periods: int = 100) -> Optional[pd.DataFrame]:
        """
        Get historical data (limited timeframes available)
        Note: FMP has limited intraday data, mainly daily
        """
        try:
            if timeframe == '1Day':
                endpoint = f"historical-price-full/{symbol}"
                params = {'timeseries': periods}
            else:
                # For intraday data, use different endpoint
                endpoint = f"historical-chart/{timeframe.lower()}/{symbol}"
                params = {}
            
            data = self._make_request(endpoint, params)
            
            if data is None:
                return None
            
            # Parse response based on endpoint
            if timeframe == '1Day':
                historical_data = data.get('historical', [])
            else:
                historical_data = data
            
            if not historical_data:
                return None
            
            # Convert to DataFrame
            df_data = []
            for item in historical_data[:periods]:
                df_data.append({
                    'timestamp': pd.to_datetime(item['date']),
                    'open': float(item['open']),
                    'high': float(item['high']),
                    'low': float(item['low']),
                    'close': float(item['close']),
                    'volume': int(item.get('volume', 0))
                })
            
            df = pd.DataFrame(df_data)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            return df
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return None
    
    def get_real_time_quote(self, symbol: str) -> Optional[Dict]:
        """Get real-time quote"""
        try:
            data = self._make_request(f"quote-short/{symbol}")
            
            if data and len(data) > 0:
                quote = data[0]
                return {
                    'symbol': symbol,
                    'price': float(quote.get('price', 0)),
                    'volume': int(quote.get('volume', 0)),
                    'timestamp': datetime.now()
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting real-time quote for {symbol}: {e}")
            return None
    
    def get_sector_performance(self) -> Optional[Dict]:
        """Get sector performance data"""
        try:
            data = self._make_request("sector-performance")
            
            if data:
                return {item['sector']: float(item['changesPercentage'].replace('%', '')) 
                       for item in data}
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting sector performance: {e}")
            return None
    
    def get_market_hours(self) -> Optional[Dict]:
        """Get market hours information"""
        try:
            data = self._make_request("market-hours")
            
            if data:
                return data
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting market hours: {e}")
            return None
    
    def search_stocks(self, query: str, limit: int = 10) -> List[Dict]:
        """Search for stocks by name or symbol"""
        try:
            params = {'query': query, 'limit': limit}
            data = self._make_request("search", params)
            
            if data:
                return data
            
            return []
            
        except Exception as e:
            logger.error(f"Error searching stocks: {e}")
            return []
