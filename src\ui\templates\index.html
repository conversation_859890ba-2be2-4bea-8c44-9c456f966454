<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTM Squeeze Trading System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line"></i> TTM Squeeze Trading System
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text" id="market-status">
                    <i class="fas fa-circle text-secondary"></i> Market Status: Loading...
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- Dashboard Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Scanner Status</h6>
                                <h4 id="scanner-status">Stopped</h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-search fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Active Alerts</h6>
                                <h4 id="alert-count">0</h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-bell fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Portfolio Value</h6>
                                <h4 id="portfolio-value">$0</h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-wallet fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Open Positions</h6>
                                <h4 id="position-count">0</h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-chart-pie fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Tabs -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="scanner-tab" data-bs-toggle="tab" data-bs-target="#scanner" type="button" role="tab">
                    <i class="fas fa-search"></i> Scanner
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis" type="button" role="tab">
                    <i class="fas fa-chart-area"></i> Analysis
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="trading-tab" data-bs-toggle="tab" data-bs-target="#trading" type="button" role="tab">
                    <i class="fas fa-exchange-alt"></i> Trading
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="risk-tab" data-bs-toggle="tab" data-bs-target="#risk" type="button" role="tab">
                    <i class="fas fa-shield-alt"></i> Risk Management
                </button>
            </li>
        </ul>

        <div class="tab-content" id="mainTabContent">
            <!-- Scanner Tab -->
            <div class="tab-pane fade show active" id="scanner" role="tabpanel">
                <div class="row mt-3">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-bell"></i> Real-time Alerts</h5>
                                <div>
                                    <button class="btn btn-success btn-sm" id="start-scanner">
                                        <i class="fas fa-play"></i> Start Scanner
                                    </button>
                                    <button class="btn btn-danger btn-sm" id="stop-scanner">
                                        <i class="fas fa-stop"></i> Stop Scanner
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="alerts-table">
                                        <thead>
                                            <tr>
                                                <th>Time</th>
                                                <th>Symbol</th>
                                                <th>Alert Type</th>
                                                <th>Message</th>
                                                <th>Strength</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td colspan="6" class="text-center text-muted">No alerts yet</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-cog"></i> Scanner Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">Scan Interval (seconds)</label>
                                    <input type="number" class="form-control" id="scan-interval" value="60">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="market-hours-only" checked>
                                        <label class="form-check-label" for="market-hours-only">
                                            Market hours only
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Scanner Statistics</label>
                                    <div id="scanner-stats" class="small text-muted">
                                        Loading...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Tab -->
            <div class="tab-pane fade" id="analysis" role="tabpanel">
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-search"></i> Symbol Analysis</h5>
                            </div>
                            <div class="card-body">
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control" id="analysis-symbol" placeholder="Enter symbol (e.g., AAPL)">
                                    <button class="btn btn-primary" id="analyze-btn">
                                        <i class="fas fa-chart-line"></i> Analyze
                                    </button>
                                </div>
                                <div id="analysis-result" class="mt-3">
                                    <!-- Analysis results will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-filter"></i> Stock Screener</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Min Price</label>
                                            <input type="number" class="form-control" id="min-price" value="10">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Max Price</label>
                                            <input type="number" class="form-control" id="max-price" value="1000">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Min Volume</label>
                                    <input type="number" class="form-control" id="min-volume" value="1000000">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="ema-alignment" checked>
                                        <label class="form-check-label" for="ema-alignment">
                                            EMA Alignment (EMA8 > EMA21)
                                        </label>
                                    </div>
                                </div>
                                <button class="btn btn-success w-100" id="run-screener">
                                    <i class="fas fa-play"></i> Run Screener
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list"></i> Screener Results</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="screener-results">
                                        <thead>
                                            <tr>
                                                <th>Symbol</th>
                                                <th>Company</th>
                                                <th>Sector</th>
                                                <th>Price</th>
                                                <th>Volume</th>
                                                <th>RSI</th>
                                                <th>Score</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td colspan="8" class="text-center text-muted">Run screener to see results</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trading Tab -->
            <div class="tab-pane fade" id="trading" role="tabpanel">
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-plus"></i> Place Order</h5>
                            </div>
                            <div class="card-body">
                                <form id="order-form">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Symbol</label>
                                                <input type="text" class="form-control" id="order-symbol" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Side</label>
                                                <select class="form-control" id="order-side" required>
                                                    <option value="buy">Buy</option>
                                                    <option value="sell">Sell</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Quantity</label>
                                                <input type="number" class="form-control" id="order-quantity" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Order Type</label>
                                                <select class="form-control" id="order-type" required>
                                                    <option value="market">Market</option>
                                                    <option value="limit">Limit</option>
                                                    <option value="stop">Stop</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3" id="price-field" style="display: none;">
                                        <label class="form-label">Price</label>
                                        <input type="number" step="0.01" class="form-control" id="order-price">
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-paper-plane"></i> Place Order
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-pie"></i> Current Positions</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm" id="positions-table">
                                        <thead>
                                            <tr>
                                                <th>Symbol</th>
                                                <th>Qty</th>
                                                <th>Avg Price</th>
                                                <th>Current</th>
                                                <th>P&L</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td colspan="6" class="text-center text-muted">No positions</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Risk Management Tab -->
            <div class="tab-pane fade" id="risk" role="tabpanel">
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-shield-alt"></i> Portfolio Risk</h5>
                            </div>
                            <div class="card-body">
                                <div id="portfolio-risk">
                                    Loading risk assessment...
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-exclamation-triangle"></i> Risk Alerts</h5>
                            </div>
                            <div class="card-body">
                                <div id="risk-alerts">
                                    No risk alerts
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
