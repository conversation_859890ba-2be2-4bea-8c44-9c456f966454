"""
Real-time Scanner for TTM Squeeze Trading System
"""
import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from queue import Queue
import json

from src.data.data_manager import DataManager
from src.indicators.multi_timeframe import MultiTimeframeA<PERSON>y<PERSON>, MultiTimeframeSignal
from src.scanner.stock_screener import StockScreener, ScreeningCriteria
from config import Config

logger = logging.getLogger(__name__)

@dataclass
class ScanAlert:
    """Scanner alert data class"""
    timestamp: datetime
    symbol: str
    alert_type: str  # 'squeeze_setup', 'squeeze_release', 'entry_signal'
    message: str
    signal: MultiTimeframeSignal
    priority: int  # 1=high, 2=medium, 3=low

class RealTimeScanner:
    """Real-time market scanner for TTM Squeeze setups"""
    
    def __init__(self):
        """Initialize real-time scanner"""
        self.data_manager = DataManager()
        self.multi_timeframe_analyzer = MultiTimeframeAnalyzer()
        self.stock_screener = StockScreener(self.data_manager)
        
        # Scanner configuration
        self.config = Config.SCANNER_CONFIG
        self.scan_interval = self.config['scan_interval']
        self.market_hours_only = self.config['market_hours_only']
        
        # Scanner state
        self.is_running = False
        self.scan_thread = None
        self.alert_queue = Queue()
        self.last_scan_time = None
        
        # Stock universe
        self.scan_universe = []
        self.universe_refresh_interval = timedelta(hours=6)
        self.last_universe_refresh = None
        
        # Alert callbacks
        self.alert_callbacks: List[Callable[[ScanAlert], None]] = []
        
        # Performance tracking
        self.scan_stats = {
            'total_scans': 0,
            'alerts_generated': 0,
            'scan_duration_avg': 0.0,
            'last_scan_duration': 0.0
        }
        
        logger.info("Real-time scanner initialized")
    
    def add_alert_callback(self, callback: Callable[[ScanAlert], None]):
        """Add callback function for alerts"""
        self.alert_callbacks.append(callback)
    
    def start_scanning(self):
        """Start the real-time scanning process"""
        if self.is_running:
            logger.warning("Scanner is already running")
            return
        
        self.is_running = True
        self.scan_thread = threading.Thread(target=self._scan_loop, daemon=True)
        self.scan_thread.start()
        
        logger.info("Real-time scanner started")
    
    def stop_scanning(self):
        """Stop the real-time scanning process"""
        self.is_running = False
        
        if self.scan_thread and self.scan_thread.is_alive():
            self.scan_thread.join(timeout=10)
        
        logger.info("Real-time scanner stopped")
    
    def _scan_loop(self):
        """Main scanning loop"""
        while self.is_running:
            try:
                # Check if we should scan
                if not self._should_scan():
                    time.sleep(60)  # Check again in 1 minute
                    continue
                
                # Refresh universe if needed
                self._refresh_universe_if_needed()
                
                # Perform scan
                scan_start_time = time.time()
                self._perform_scan()
                scan_duration = time.time() - scan_start_time
                
                # Update statistics
                self._update_scan_stats(scan_duration)
                
                # Wait for next scan
                time.sleep(self.scan_interval)
                
            except Exception as e:
                logger.error(f"Error in scan loop: {e}")
                time.sleep(60)  # Wait before retrying
    
    def _should_scan(self) -> bool:
        """Check if we should perform a scan"""

        # Check market hours if configured
        if self.market_hours_only and not self.data_manager.is_market_open():
            logger.debug("Scan skipped: Market is closed and market_hours_only is True")
            return False

        # Check if we have a universe to scan
        if not self.scan_universe:
            logger.debug(f"Scan skipped: Universe is empty (size: {len(self.scan_universe)})")
            return False

        logger.debug(f"Scan conditions met: market_hours_only={self.market_hours_only}, universe_size={len(self.scan_universe)}")
        return True
    
    def _refresh_universe_if_needed(self):
        """Refresh scan universe if needed"""
        now = datetime.now()

        if (self.last_universe_refresh is None or
            now - self.last_universe_refresh > self.universe_refresh_interval):

            logger.info("Refreshing scan universe...")
            logger.debug(f"Current universe size: {len(self.scan_universe)}")

            try:
                # Get screened universe with relaxed criteria for broader coverage
                criteria = ScreeningCriteria(
                    min_price=Config.STOCK_UNIVERSE['min_price'],
                    min_volume=Config.STOCK_UNIVERSE['min_volume'],
                    min_market_cap=Config.STOCK_UNIVERSE['min_market_cap'],
                    min_rsi=10.0,  # Very relaxed RSI range
                    max_rsi=90.0,  # Very relaxed RSI range
                    ema_alignment=False  # Don't require EMA alignment for broader coverage
                )

                max_stocks = Config.STOCK_UNIVERSE.get('max_stocks', 1000)
                logger.debug(f"Screening criteria: min_price={criteria.min_price}, min_volume={criteria.min_volume}, min_market_cap={criteria.min_market_cap}, max_stocks={max_stocks}")

                screened_stocks = self.stock_screener.screen_stocks(criteria, max_stocks=max_stocks)
                logger.debug(f"Screened stocks returned: {len(screened_stocks) if screened_stocks else 0} stocks")

                if screened_stocks:
                    self.scan_universe = [stock.symbol for stock in screened_stocks]
                    logger.info(f"Universe refreshed with {len(self.scan_universe)} symbols")
                    logger.debug(f"Sample symbols: {self.scan_universe[:10]}")
                else:
                    logger.warning("No stocks returned from screening")

                self.last_universe_refresh = now

            except Exception as e:
                logger.error(f"Error refreshing universe: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
    
    def _perform_scan(self):
        """Perform a single scan of the universe"""
        if not self.scan_universe:
            return
        
        logger.debug(f"Scanning {len(self.scan_universe)} symbols...")
        
        # Process symbols in batches
        batch_size = 20
        alerts_generated = 0
        
        for i in range(0, len(self.scan_universe), batch_size):
            if not self.is_running:
                break
            
            batch = self.scan_universe[i:i + batch_size]
            batch_alerts = self._scan_batch(batch)
            alerts_generated += len(batch_alerts)
            
            # Process alerts
            for alert in batch_alerts:
                self._process_alert(alert)
        
        self.last_scan_time = datetime.now()
        logger.debug(f"Scan completed. Generated {alerts_generated} alerts")
    
    def _scan_batch(self, symbols: List[str]) -> List[ScanAlert]:
        """Scan a batch of symbols"""
        alerts = []
        
        try:
            # Get data for all timeframes
            timeframes = [Config.TIMEFRAMES['primary']] + Config.TIMEFRAMES['confirmation']
            
            for symbol in symbols:
                if not self.is_running:
                    break
                
                try:
                    # Get multi-timeframe data
                    data_dict = {}
                    for timeframe in timeframes:
                        data = self.data_manager.get_historical_data(symbol, timeframe, periods=50)
                        if data is not None and len(data) >= 30:
                            data_dict[timeframe] = data
                    
                    if len(data_dict) < 2:  # Need at least 2 timeframes
                        continue
                    
                    # Analyze with multi-timeframe analyzer
                    signal = self.multi_timeframe_analyzer.analyze_symbol(symbol, data_dict)
                    
                    if signal:
                        # Check for alert conditions
                        alert = self._check_alert_conditions(signal)
                        if alert:
                            alerts.append(alert)
                
                except Exception as e:
                    logger.debug(f"Error scanning {symbol}: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"Error in batch scan: {e}")
        
        return alerts
    
    def _check_alert_conditions(self, signal: MultiTimeframeSignal) -> Optional[ScanAlert]:
        """Check if signal meets alert conditions"""
        
        # High priority alerts
        if (signal.primary_signal.entry_signal and 
            signal.is_valid_setup and 
            signal.overall_strength > 0.8):
            
            return ScanAlert(
                timestamp=datetime.now(),
                symbol=signal.symbol,
                alert_type='entry_signal',
                message=f"Strong entry signal: {signal.entry_recommendation}",
                signal=signal,
                priority=1
            )
        
        # Medium priority alerts
        if signal.is_valid_setup and signal.overall_strength > 0.6:
            return ScanAlert(
                timestamp=datetime.now(),
                symbol=signal.symbol,
                alert_type='squeeze_setup',
                message=f"TTM Squeeze setup detected: {signal.confirmations_count}/{signal.required_confirmations} confirmations",
                signal=signal,
                priority=2
            )
        
        # Squeeze release alerts
        if (signal.primary_signal.is_squeeze and 
            any(not conf_signal.is_squeeze for conf_signal in signal.confirmation_signals.values())):
            
            return ScanAlert(
                timestamp=datetime.now(),
                symbol=signal.symbol,
                alert_type='squeeze_release',
                message="Potential squeeze release detected",
                signal=signal,
                priority=2
            )
        
        return None
    
    def _process_alert(self, alert: ScanAlert):
        """Process and distribute alert"""
        
        # Add to queue
        self.alert_queue.put(alert)
        
        # Update statistics
        self.scan_stats['alerts_generated'] += 1
        
        # Call registered callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"Error in alert callback: {e}")
        
        # Log alert
        logger.info(f"ALERT [{alert.priority}]: {alert.symbol} - {alert.message}")
    
    def _update_scan_stats(self, scan_duration: float):
        """Update scanning statistics"""
        self.scan_stats['total_scans'] += 1
        self.scan_stats['last_scan_duration'] = scan_duration
        
        # Update average duration
        total_scans = self.scan_stats['total_scans']
        current_avg = self.scan_stats['scan_duration_avg']
        self.scan_stats['scan_duration_avg'] = (
            (current_avg * (total_scans - 1) + scan_duration) / total_scans
        )
    
    def get_recent_alerts(self, limit: int = 50) -> List[ScanAlert]:
        """Get recent alerts from queue"""
        alerts = []
        
        while not self.alert_queue.empty() and len(alerts) < limit:
            try:
                alert = self.alert_queue.get_nowait()
                alerts.append(alert)
            except:
                break
        
        return alerts
    
    def get_scan_stats(self) -> Dict:
        """Get scanning statistics"""
        stats = self.scan_stats.copy()
        stats.update({
            'is_running': self.is_running,
            'universe_size': len(self.scan_universe),
            'last_scan_time': self.last_scan_time,
            'last_universe_refresh': self.last_universe_refresh,
            'alerts_in_queue': self.alert_queue.qsize()
        })
        
        return stats
    
    def manual_scan_symbol(self, symbol: str) -> Optional[MultiTimeframeSignal]:
        """Manually scan a specific symbol"""
        try:
            timeframes = [Config.TIMEFRAMES['primary']] + Config.TIMEFRAMES['confirmation']
            
            # Get data for all timeframes
            data_dict = {}
            for timeframe in timeframes:
                data = self.data_manager.get_historical_data(symbol, timeframe, periods=100)
                if data is not None and len(data) >= 30:
                    data_dict[timeframe] = data
            
            if len(data_dict) < 2:
                logger.warning(f"Insufficient data for {symbol}")
                return None
            
            # Analyze with multi-timeframe analyzer
            signal = self.multi_timeframe_analyzer.analyze_symbol(symbol, data_dict)
            
            if signal:
                logger.info(f"Manual scan result for {symbol}: {signal.entry_recommendation}")
            
            return signal
            
        except Exception as e:
            logger.error(f"Error in manual scan for {symbol}: {e}")
            return None
    
    def export_alerts(self, filename: str = None) -> str:
        """Export recent alerts to file"""
        if filename is None:
            filename = f"scanner_alerts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        alerts = self.get_recent_alerts(1000)  # Get up to 1000 recent alerts
        
        # Convert alerts to serializable format
        alert_data = []
        for alert in alerts:
            alert_data.append({
                'timestamp': alert.timestamp.isoformat(),
                'symbol': alert.symbol,
                'alert_type': alert.alert_type,
                'message': alert.message,
                'priority': alert.priority,
                'entry_recommendation': alert.signal.entry_recommendation,
                'overall_strength': alert.signal.overall_strength,
                'confirmations': alert.signal.confirmations_count
            })
        
        with open(filename, 'w') as f:
            json.dump(alert_data, f, indent=2)
        
        logger.info(f"Exported {len(alert_data)} alerts to {filename}")
        return filename
