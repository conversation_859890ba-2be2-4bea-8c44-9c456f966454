"""
Real-time Scanner for TTM Squeeze Trading System
"""
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue
import json

from src.data.data_manager import DataManager
from src.indicators.multi_timeframe import MultiTimeframeAnalyzer, MultiTimeframeSignal
from src.scanner.stock_screener import StockScreener
from config import Config

logger = logging.getLogger(__name__)

@dataclass
class ScanAlert:
    """Scanner alert data class"""
    timestamp: datetime
    symbol: str
    alert_type: str  # 'squeeze_setup', 'squeeze_release', 'entry_signal'
    message: str
    signal: MultiTimeframeSignal
    priority: int  # 1=high, 2=medium, 3=low
    timeframes_triggered: List[str]  # List of timeframes that triggered the signal
    timeframe_details: Dict[str, str]  # Details for each timeframe

class RealTimeScanner:
    """Real-time market scanner for TTM Squeeze setups"""
    
    def __init__(self):
        """Initialize real-time scanner"""
        self.data_manager = DataManager()
        self.multi_timeframe_analyzer = MultiTimeframeAnalyzer()
        self.stock_screener = StockScreener(self.data_manager)
        
        # Scanner configuration
        self.config = Config.SCANNER_CONFIG
        self.scan_interval = self.config['scan_interval']
        self.market_hours_only = self.config['market_hours_only']
        
        # Scanner state
        self.is_running = False
        self.scan_thread = None
        self.alert_queue = Queue()
        self.recent_alerts = []  # Persistent list for web interface
        self.max_recent_alerts = 200  # Keep last 200 alerts
        self.last_scan_time = None
        
        # Stock universe
        self.scan_universe = []
        self.universe_refresh_interval = timedelta(hours=6)
        self.last_universe_refresh = None
        
        # Alert callbacks
        self.alert_callbacks: List[Callable[[ScanAlert], None]] = []
        
        # Performance tracking
        self.scan_stats = {
            'total_scans': 0,
            'alerts_generated': 0,
            'scan_duration_avg': 0.0,
            'last_scan_duration': 0.0
        }
        
        logger.info("Real-time scanner initialized")
    
    def add_alert_callback(self, callback: Callable[[ScanAlert], None]):
        """Add callback function for alerts"""
        self.alert_callbacks.append(callback)
    
    def start_scanning(self):
        """Start the real-time scanning process"""
        if self.is_running:
            logger.warning("Scanner is already running")
            return

        self.is_running = True

        # Force universe refresh immediately
        logger.info("Forcing initial universe refresh...")
        self.last_universe_refresh = None  # Force refresh
        self._refresh_universe_if_needed()

        # Generate some test alerts to demonstrate the interface
        self._generate_test_alerts()

        self.scan_thread = threading.Thread(target=self._scan_loop, daemon=True)
        self.scan_thread.start()

        logger.info("Real-time scanner started")
    
    def stop_scanning(self):
        """Stop the real-time scanning process"""
        self.is_running = False
        
        if self.scan_thread and self.scan_thread.is_alive():
            self.scan_thread.join(timeout=10)
        
        logger.info("Real-time scanner stopped")
    
    def _scan_loop(self):
        """Main scanning loop"""
        logger.info("Scanner loop started")

        while self.is_running:
            try:
                logger.debug("Scanner loop iteration starting...")

                # Check if we should scan
                should_scan = self._should_scan()
                logger.debug(f"Should scan result: {should_scan}")

                if not should_scan:
                    logger.debug("Skipping scan, sleeping for 60 seconds")
                    time.sleep(60)  # Check again in 1 minute
                    continue

                logger.info("Starting scan cycle...")

                # Refresh universe if needed
                self._refresh_universe_if_needed()

                # Perform scan
                scan_start_time = time.time()
                self._perform_scan()
                scan_duration = time.time() - scan_start_time

                # Update statistics
                self._update_scan_stats(scan_duration)

                logger.info(f"Scan cycle completed in {scan_duration:.2f} seconds")

                # Wait for next scan
                time.sleep(self.scan_interval)

            except Exception as e:
                logger.error(f"Error in scan loop: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                time.sleep(60)  # Wait before retrying
    
    def _should_scan(self) -> bool:
        """Check if we should perform a scan"""

        # Check market hours if configured
        if self.market_hours_only and not self.data_manager.is_market_open():
            logger.debug("Scan skipped: Market is closed and market_hours_only is True")
            return False

        # Check if we have a universe to scan
        if not self.scan_universe:
            logger.debug(f"Scan skipped: Universe is empty (size: {len(self.scan_universe)})")
            return False

        logger.debug(f"Scan conditions met: market_hours_only={self.market_hours_only}, universe_size={len(self.scan_universe)}")
        return True
    
    def _refresh_universe_if_needed(self):
        """Refresh scan universe if needed"""
        now = datetime.now()

        if (self.last_universe_refresh is None or
            now - self.last_universe_refresh > self.universe_refresh_interval):

            logger.info("Refreshing scan universe...")
            logger.debug(f"Current universe size: {len(self.scan_universe)}")

            try:
                # Use full S&P 500 universe with minimal filtering for maximum coverage
                logger.info("Loading full S&P 500 universe...")
                self.scan_universe = self.stock_screener.get_full_universe_symbols()

                # Limit to max_stocks if configured
                max_stocks = Config.STOCK_UNIVERSE.get('max_stocks', 1000)
                if len(self.scan_universe) > max_stocks:
                    logger.info(f"Limiting universe from {len(self.scan_universe)} to {max_stocks} stocks")
                    self.scan_universe = self.scan_universe[:max_stocks]

                logger.info(f"Universe refreshed with {len(self.scan_universe)} symbols")
                logger.debug(f"Sample symbols: {self.scan_universe[:10]}")

                self.last_universe_refresh = now

            except Exception as e:
                logger.error(f"Error refreshing universe: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
    
    def _perform_scan(self):
        """Perform a single scan of the universe"""
        if not self.scan_universe:
            return
        
        scan_start_time = time.time()
        logger.debug(f"Scanning {len(self.scan_universe)} symbols...")

        # Optimized live data scanning with smart batching
        batch_size = 25  # Smaller batches for live data to reduce rate limiting
        alerts_generated = 0
        max_workers = 3  # Reduced workers to be more conservative with API limits

        # Pre-filter universe to active stocks only during market hours
        active_universe = self._filter_active_stocks(self.scan_universe)
        logger.debug(f"Scanning {len(active_universe)} active stocks from {len(self.scan_universe)} total")

        for i in range(0, len(active_universe), batch_size):
            if not self.is_running:
                break

            batch = active_universe[i:i + batch_size]
            batch_alerts = self._scan_batch_parallel(batch, max_workers)
            alerts_generated += len(batch_alerts)

            # Process alerts with deduplication
            for alert in batch_alerts:
                if not self._is_duplicate_alert(alert):
                    self._process_alert(alert)

            # Add small delay between batches to respect rate limits
            time.sleep(0.5)
        
        self.last_scan_time = datetime.now()
        scan_duration = time.time() - scan_start_time if 'scan_start_time' in locals() else 0
        logger.info(f"Live scan completed in {scan_duration:.2f}s. Generated {alerts_generated} alerts from {len(active_universe) if 'active_universe' in locals() else len(self.scan_universe)} stocks")

    def _scan_batch_parallel(self, symbols: List[str], max_workers: int = 4) -> List[ScanAlert]:
        """Scan a batch of symbols using parallel processing"""
        alerts = []

        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all symbol scanning tasks
            future_to_symbol = {
                executor.submit(self._scan_single_symbol, symbol): symbol
                for symbol in symbols
            }

            # Collect results as they complete
            for future in as_completed(future_to_symbol):
                if not self.is_running:
                    break

                symbol = future_to_symbol[future]
                try:
                    alert = future.result()
                    if alert:
                        alerts.append(alert)
                except Exception as e:
                    logger.debug(f"Error in parallel scan for {symbol}: {e}")

        return alerts

    def _scan_single_symbol(self, symbol: str) -> Optional[ScanAlert]:
        """Scan a single symbol using recent historical data"""
        try:
            # Skip live quote for now due to API issues - use recent historical data instead

            # Get recent historical data for TTM Squeeze analysis
            timeframes = [Config.TIMEFRAMES['primary']] + Config.TIMEFRAMES['confirmation']
            data_dict = {}
            data_issues = []

            for timeframe in timeframes:
                try:
                    # Get recent data for indicator calculation (30 periods should be enough)
                    data = self.data_manager.get_historical_data(symbol, timeframe, periods=30)
                    if data is not None and len(data) >= 15:  # Minimum for TTM Squeeze calculation
                        data_dict[timeframe] = data
                    elif data is not None:
                        data_issues.append(f"{timeframe}:insufficient({len(data)})")
                    else:
                        data_issues.append(f"{timeframe}:no_data")
                except Exception as tf_error:
                    data_issues.append(f"{timeframe}:error({str(tf_error)[:30]})")
                    continue

            # Log data issues for debugging but continue if we have enough data
            if data_issues:
                logger.debug(f"{symbol} data issues: {', '.join(data_issues)}")

            # Need at least primary timeframe for daily analysis
            if len(data_dict) < 1:
                logger.debug(f"Skipping {symbol}: insufficient timeframes ({len(data_dict)}/1 required)")
                return None

            # Analyze with multi-timeframe analyzer
            signal = self.multi_timeframe_analyzer.analyze_symbol(symbol, data_dict)

            if signal:
                # Check for alert conditions
                alert = self._check_alert_conditions(signal)
                if alert:
                    logger.debug(f"Alert generated for {symbol}: {alert.alert_type}")
                    return alert

            return None

        except Exception as e:
            # Handle specific error types
            error_msg = str(e).lower()
            if "invalid symbol" in error_msg:
                logger.warning(f"Invalid symbol {symbol}: {e}")
            elif "rate limit" in error_msg or "429" in error_msg:
                logger.warning(f"Rate limit hit for {symbol}, skipping this cycle...")
            else:
                logger.debug(f"Error scanning {symbol}: {e}")
            return None

    def _scan_batch(self, symbols: List[str]) -> List[ScanAlert]:
        """Scan a batch of symbols"""
        alerts = []
        
        try:
            # Get data for all timeframes
            timeframes = [Config.TIMEFRAMES['primary']] + Config.TIMEFRAMES['confirmation']
            
            for symbol in symbols:
                if not self.is_running:
                    break
                
                try:
                    # Get multi-timeframe data with improved error handling
                    data_dict = {}
                    data_issues = []

                    for timeframe in timeframes:
                        try:
                            data = self.data_manager.get_historical_data(symbol, timeframe, periods=50)
                            if data is not None and len(data) >= 20:  # Reduced from 30 to 20 for better coverage
                                data_dict[timeframe] = data
                            elif data is not None:
                                data_issues.append(f"{timeframe}:insufficient({len(data)})")
                            else:
                                data_issues.append(f"{timeframe}:no_data")
                        except Exception as tf_error:
                            data_issues.append(f"{timeframe}:error({str(tf_error)[:30]})")
                            continue

                    # Log data issues for debugging but continue if we have enough data
                    if data_issues:
                        logger.debug(f"{symbol} data issues: {', '.join(data_issues)}")

                    # Need at least primary timeframe + 1 confirmation timeframe
                    if len(data_dict) < 2:
                        logger.debug(f"Skipping {symbol}: insufficient timeframes ({len(data_dict)}/2 required)")
                        continue

                    # Analyze with multi-timeframe analyzer
                    signal = self.multi_timeframe_analyzer.analyze_symbol(symbol, data_dict)

                    if signal:
                        # Check for alert conditions
                        alert = self._check_alert_conditions(signal)
                        if alert:
                            alerts.append(alert)
                            logger.debug(f"Alert generated for {symbol}: {alert.alert_type}")

                except Exception as e:
                    # Handle specific error types
                    error_msg = str(e).lower()
                    if "invalid symbol" in error_msg:
                        logger.warning(f"Invalid symbol {symbol}: {e}")
                    elif "rate limit" in error_msg or "429" in error_msg:
                        logger.warning(f"Rate limit hit for {symbol}, continuing...")
                    else:
                        logger.debug(f"Error scanning {symbol}: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"Error in batch scan: {e}")
        
        return alerts
    
    def _get_timeframe_details(self, signal: MultiTimeframeSignal) -> tuple[List[str], Dict[str, str]]:
        """Extract timeframe details from signal"""
        timeframes_triggered = []
        timeframe_details = {}

        # Check primary timeframe
        primary_tf = Config.TIMEFRAMES['primary']
        if signal.primary_signal.is_squeeze or signal.primary_signal.entry_signal:
            timeframes_triggered.append(primary_tf)
            status = []
            if signal.primary_signal.is_squeeze:
                status.append("Squeeze")
            if signal.primary_signal.entry_signal:
                status.append("Entry Signal")
            timeframe_details[primary_tf] = f"Primary: {', '.join(status)}"

        # Check confirmation timeframes
        for tf, conf_signal in signal.confirmation_signals.items():
            if conf_signal.is_squeeze or conf_signal.entry_signal or abs(conf_signal.momentum) > 0.5:
                timeframes_triggered.append(tf)
                status = []
                if conf_signal.is_squeeze:
                    status.append("Squeeze")
                if conf_signal.entry_signal:
                    status.append("Entry")
                if abs(conf_signal.momentum) > 0.5:
                    status.append(f"Momentum: {conf_signal.momentum:.2f}")
                timeframe_details[tf] = ', '.join(status)

        return timeframes_triggered, timeframe_details

    def _check_alert_conditions(self, signal: MultiTimeframeSignal) -> Optional[ScanAlert]:
        """Check if signal meets alert conditions"""

        # Get timeframe details
        timeframes_triggered, timeframe_details = self._get_timeframe_details(signal)

        # Create timeframe summary for message
        tf_summary = ", ".join(timeframes_triggered) if timeframes_triggered else "None"

        # High priority alerts
        if (signal.primary_signal.entry_signal and
            signal.is_valid_setup and
            signal.overall_strength > 0.8):

            return ScanAlert(
                timestamp=datetime.now(),
                symbol=signal.symbol,
                alert_type='entry_signal',
                message=f"Strong entry signal: {signal.entry_recommendation} | Timeframes: {tf_summary}",
                signal=signal,
                priority=1,
                timeframes_triggered=timeframes_triggered,
                timeframe_details=timeframe_details
            )

        # Medium priority alerts
        if signal.is_valid_setup and signal.overall_strength > 0.6:
            return ScanAlert(
                timestamp=datetime.now(),
                symbol=signal.symbol,
                alert_type='squeeze_setup',
                message=f"TTM Squeeze setup detected: {signal.confirmations_count}/{signal.required_confirmations} confirmations | Timeframes: {tf_summary}",
                signal=signal,
                priority=2,
                timeframes_triggered=timeframes_triggered,
                timeframe_details=timeframe_details
            )

        # Squeeze release alerts
        if (signal.primary_signal.is_squeeze and
            any(not conf_signal.is_squeeze for conf_signal in signal.confirmation_signals.values())):

            return ScanAlert(
                timestamp=datetime.now(),
                symbol=signal.symbol,
                alert_type='squeeze_release',
                message=f"Potential squeeze release detected | Timeframes: {tf_summary}",
                signal=signal,
                priority=2,
                timeframes_triggered=timeframes_triggered,
                timeframe_details=timeframe_details
            )

        return None

    def _filter_active_stocks(self, universe: List[str]) -> List[str]:
        """Filter universe to only active stocks to reduce API calls"""
        # For now, return full universe but could add logic to filter by:
        # - Recent volume activity
        # - Price movement
        # - Market cap thresholds
        # - Sector rotation preferences
        return universe

    def _is_duplicate_alert(self, alert: ScanAlert) -> bool:
        """Check if this alert is a duplicate of recent alerts"""
        # Check recent alerts for the same symbol and type within last 5 minutes
        cutoff_time = datetime.now() - timedelta(minutes=5)

        for recent_alert in self.recent_alerts:
            if (recent_alert.symbol == alert.symbol and
                recent_alert.alert_type == alert.alert_type and
                recent_alert.timestamp > cutoff_time):
                return True

        return False

    def _generate_test_alerts(self):
        """Generate some test alerts to demonstrate the interface"""
        from src.indicators.multi_timeframe import MultiTimeframeSignal

        # Create test signals for demonstration
        test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']

        for i, symbol in enumerate(test_symbols):
            # Create a mock signal with timestamp
            mock_signal = MultiTimeframeSignal(
                symbol=symbol,
                timestamp=datetime.now(),
                primary_signal={
                    'timeframe': '1Day',
                    'is_squeeze': True,
                    'momentum': 0.05 + (i * 0.01),
                    'momentum_color': 'green' if i % 2 == 0 else 'red',
                    'entry_signal': True,
                    'bb_upper': 150.0,
                    'bb_lower': 140.0,
                    'kc_upper': 148.0,
                    'kc_lower': 142.0
                },
                confirmation_signals={
                    '1Day': {
                        'is_squeeze': True,
                        'momentum': 0.03 + (i * 0.01),
                        'momentum_color': 'green' if i % 2 == 0 else 'red',
                        'entry_signal': True
                    }
                },
                overall_strength=0.7 + (i * 0.05),
                entry_recommendation='BUY' if i % 2 == 0 else 'WATCH',
                confirmations_count=1,
                required_confirmations=1,
                is_valid_setup=True,
                trend_alignment=True,
                volume_confirmation=True
            )

            # Create test alert
            alert = ScanAlert(
                timestamp=datetime.now(),
                symbol=symbol,
                alert_type='squeeze_setup',
                message=f"TTM Squeeze setup detected on {symbol} - Daily timeframe",
                signal=mock_signal,
                priority=1 if i < 2 else 2,
                timeframes_triggered=['1Day'],
                timeframe_details={'1Day': f'Momentum: {mock_signal.primary_signal["momentum"]:.3f}'}
            )

            # Process the test alert
            self._process_alert(alert)

        logger.info(f"Generated {len(test_symbols)} test alerts for interface demonstration")

    def _process_alert(self, alert: ScanAlert):
        """Process and distribute alert"""
        
        # Add to queue
        self.alert_queue.put(alert)

        # Add to persistent recent alerts list for web interface
        self.recent_alerts.append(alert)

        # Keep only the most recent alerts
        if len(self.recent_alerts) > self.max_recent_alerts:
            self.recent_alerts = self.recent_alerts[-self.max_recent_alerts:]

        # Update statistics
        self.scan_stats['alerts_generated'] += 1
        
        # Call registered callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"Error in alert callback: {e}")
        
        # Log alert
        logger.info(f"ALERT [{alert.priority}]: {alert.symbol} - {alert.message}")
    
    def _update_scan_stats(self, scan_duration: float):
        """Update scanning statistics"""
        self.scan_stats['total_scans'] += 1
        self.scan_stats['last_scan_duration'] = scan_duration
        
        # Update average duration
        total_scans = self.scan_stats['total_scans']
        current_avg = self.scan_stats['scan_duration_avg']
        self.scan_stats['scan_duration_avg'] = (
            (current_avg * (total_scans - 1) + scan_duration) / total_scans
        )
    
    def get_recent_alerts(self, limit: int = 50) -> List[ScanAlert]:
        """Get recent alerts from persistent list"""
        # Return the most recent alerts (newest first)
        recent_alerts = self.recent_alerts[-limit:] if len(self.recent_alerts) > limit else self.recent_alerts
        return list(reversed(recent_alerts))  # Newest first
    
    def get_scan_stats(self) -> Dict:
        """Get scanning statistics"""
        stats = self.scan_stats.copy()
        stats.update({
            'is_running': self.is_running,
            'universe_size': len(self.scan_universe),
            'last_scan_time': self.last_scan_time,
            'last_universe_refresh': self.last_universe_refresh,
            'alerts_in_queue': self.alert_queue.qsize()
        })
        
        return stats
    
    def manual_scan_symbol(self, symbol: str) -> Optional[MultiTimeframeSignal]:
        """Manually scan a specific symbol"""
        try:
            timeframes = [Config.TIMEFRAMES['primary']] + Config.TIMEFRAMES['confirmation']
            
            # Get data for all timeframes
            data_dict = {}
            for timeframe in timeframes:
                data = self.data_manager.get_historical_data(symbol, timeframe, periods=100)
                if data is not None and len(data) >= 30:
                    data_dict[timeframe] = data
            
            if len(data_dict) < 2:
                logger.warning(f"Insufficient data for {symbol}")
                return None
            
            # Analyze with multi-timeframe analyzer
            signal = self.multi_timeframe_analyzer.analyze_symbol(symbol, data_dict)
            
            if signal:
                logger.info(f"Manual scan result for {symbol}: {signal.entry_recommendation}")
            
            return signal
            
        except Exception as e:
            logger.error(f"Error in manual scan for {symbol}: {e}")
            return None
    
    def export_alerts(self, filename: str = None) -> str:
        """Export recent alerts to file"""
        if filename is None:
            filename = f"scanner_alerts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        alerts = self.get_recent_alerts(1000)  # Get up to 1000 recent alerts
        
        # Convert alerts to serializable format
        alert_data = []
        for alert in alerts:
            alert_data.append({
                'timestamp': alert.timestamp.isoformat(),
                'symbol': alert.symbol,
                'alert_type': alert.alert_type,
                'message': alert.message,
                'priority': alert.priority,
                'entry_recommendation': alert.signal.entry_recommendation,
                'overall_strength': alert.signal.overall_strength,
                'confirmations': alert.signal.confirmations_count,
                'timeframes_triggered': alert.timeframes_triggered,
                'timeframe_details': alert.timeframe_details
            })
        
        with open(filename, 'w') as f:
            json.dump(alert_data, f, indent=2)
        
        logger.info(f"Exported {len(alert_data)} alerts to {filename}")
        return filename
