"""
AI Signal Enhancer - Main orchestrator for AI-powered TTM Squeeze enhancements
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

from ..indicators.multi_timeframe import MultiTimeframeSignal
from .signal_quality_model import SignalQualityPredictor
from .market_context_analyzer import MarketContextAnalyzer
from .adaptive_optimizer import AdaptiveParameterOptimizer
from .risk_intelligence import RiskAssessmentEngine

logger = logging.getLogger(__name__)

@dataclass
class AIEnhancedSignal:
    """Enhanced signal with AI-powered analysis"""
    # Original signal
    original_signal: MultiTimeframeSignal
    
    # AI enhancements
    ai_quality_score: float  # 0-100, ML-predicted signal reliability
    ai_confidence: float     # 0-100, confidence in the prediction
    market_context_score: float  # 0-100, market favorability
    risk_assessment: Dict    # Position sizing, stop loss, etc.
    
    # Combined scores
    final_score: float       # 0-100, overall signal strength
    recommendation: str      # 'strong_buy', 'buy', 'hold', 'avoid'
    
    # Additional context
    market_regime: str       # 'trending', 'ranging', 'volatile', 'calm'
    sector_strength: float   # Sector relative performance
    news_sentiment: float    # -1 to 1, news sentiment
    
    # Trade parameters
    suggested_position_size: float
    suggested_stop_loss: float
    suggested_take_profit: float
    max_risk_per_trade: float

class AISignalEnhancer:
    """Main AI enhancement engine for TTM Squeeze signals"""
    
    def __init__(self, data_manager):
        self.data_manager = data_manager
        
        # Initialize AI components
        self.quality_predictor = SignalQualityPredictor()
        self.market_analyzer = MarketContextAnalyzer(data_manager)
        self.parameter_optimizer = AdaptiveParameterOptimizer()
        self.risk_engine = RiskAssessmentEngine()
        
        # Model training status
        self.models_trained = False
        self.last_training_date = None
        
        logger.info("AI Signal Enhancer initialized")
    
    async def enhance_signal(self, signal: MultiTimeframeSignal, 
                           market_data: Dict) -> AIEnhancedSignal:
        """
        Enhance a TTM Squeeze signal with AI analysis
        
        Args:
            signal: Original MultiTimeframeSignal
            market_data: Current market data and context
            
        Returns:
            AIEnhancedSignal with AI enhancements
        """
        try:
            # 1. Predict signal quality using ML
            quality_score, confidence = await self._predict_signal_quality(signal, market_data)
            
            # 2. Analyze market context
            market_context = await self._analyze_market_context(signal.symbol, market_data)
            
            # 3. Assess risk and position sizing
            risk_assessment = await self._assess_risk(signal, market_context)
            
            # 4. Calculate final combined score
            final_score = self._calculate_final_score(
                signal.overall_strength * 100,
                quality_score,
                market_context['favorability_score']
            )
            
            # 5. Generate recommendation
            recommendation = self._generate_recommendation(final_score, market_context)
            
            # Create enhanced signal
            enhanced_signal = AIEnhancedSignal(
                original_signal=signal,
                ai_quality_score=quality_score,
                ai_confidence=confidence,
                market_context_score=market_context['favorability_score'],
                risk_assessment=risk_assessment,
                final_score=final_score,
                recommendation=recommendation,
                market_regime=market_context['regime'],
                sector_strength=market_context['sector_strength'],
                news_sentiment=market_context['news_sentiment'],
                suggested_position_size=risk_assessment['position_size'],
                suggested_stop_loss=risk_assessment['stop_loss'],
                suggested_take_profit=risk_assessment['take_profit'],
                max_risk_per_trade=risk_assessment['max_risk']
            )
            
            logger.info(f"Enhanced signal for {signal.symbol}: "
                       f"Final Score: {final_score:.1f}, "
                       f"Recommendation: {recommendation}")
            
            return enhanced_signal
            
        except Exception as e:
            logger.error(f"Error enhancing signal for {signal.symbol}: {e}")
            # Return basic enhancement on error
            return self._create_fallback_signal(signal)
    
    async def _predict_signal_quality(self, signal: MultiTimeframeSignal, 
                                    market_data: Dict) -> Tuple[float, float]:
        """Predict signal quality using ML model"""
        try:
            # Extract features for ML model
            features = self._extract_signal_features(signal, market_data)
            
            # Get prediction from ML model
            quality_score, confidence = self.quality_predictor.predict(features)
            
            return quality_score, confidence
            
        except Exception as e:
            logger.warning(f"Error predicting signal quality: {e}")
            # Fallback to original signal strength
            return signal.overall_strength * 100, 50.0
    
    async def _analyze_market_context(self, symbol: str, 
                                    market_data: Dict) -> Dict:
        """Analyze broader market context"""
        try:
            context = await self.market_analyzer.analyze_context(symbol, market_data)
            return context
            
        except Exception as e:
            logger.warning(f"Error analyzing market context: {e}")
            return {
                'favorability_score': 50.0,
                'regime': 'unknown',
                'sector_strength': 0.0,
                'news_sentiment': 0.0
            }
    
    async def _assess_risk(self, signal: MultiTimeframeSignal, 
                         market_context: Dict) -> Dict:
        """Assess risk and calculate position sizing"""
        try:
            risk_assessment = self.risk_engine.assess_risk(
                signal=signal,
                market_context=market_context
            )
            return risk_assessment
            
        except Exception as e:
            logger.warning(f"Error assessing risk: {e}")
            return {
                'position_size': 1.0,
                'stop_loss': 0.02,
                'take_profit': 0.04,
                'max_risk': 0.01
            }
    
    def _extract_signal_features(self, signal: MultiTimeframeSignal, 
                               market_data: Dict) -> np.ndarray:
        """Extract features for ML model"""
        features = []
        
        # Signal characteristics
        features.extend([
            signal.overall_strength,
            signal.confirmations_count,
            signal.required_confirmations,
            float(signal.is_valid_setup),
            float(signal.trend_alignment),
            float(signal.volume_confirmation)
        ])
        
        # Primary signal features
        primary = signal.primary_signal
        features.extend([
            float(primary.is_squeeze),
            primary.momentum,
            primary.signal_strength,
            float(primary.entry_signal)
        ])
        
        # Timeframe alignment features
        timeframe_count = len(signal.confirmation_signals)
        squeeze_count = sum(1 for s in signal.confirmation_signals.values() if s.is_squeeze)
        entry_count = sum(1 for s in signal.confirmation_signals.values() if s.entry_signal)
        
        features.extend([
            timeframe_count,
            squeeze_count / max(timeframe_count, 1),
            entry_count / max(timeframe_count, 1)
        ])
        
        # Market context features (if available)
        if market_data:
            features.extend([
                market_data.get('vix', 20.0),
                market_data.get('spy_rsi', 50.0),
                market_data.get('sector_rotation', 0.0)
            ])
        else:
            features.extend([20.0, 50.0, 0.0])
        
        return np.array(features, dtype=np.float32)
    
    def _calculate_final_score(self, original_score: float, 
                             quality_score: float, 
                             market_score: float) -> float:
        """Calculate weighted final score"""
        # Weighted combination
        weights = {
            'original': 0.4,    # Original TTM Squeeze strength
            'quality': 0.35,    # AI quality prediction
            'market': 0.25      # Market context
        }
        
        final_score = (
            original_score * weights['original'] +
            quality_score * weights['quality'] +
            market_score * weights['market']
        )
        
        return min(max(final_score, 0.0), 100.0)
    
    def _generate_recommendation(self, final_score: float, 
                               market_context: Dict) -> str:
        """Generate trading recommendation"""
        # Base recommendation on final score
        if final_score >= 85:
            base_rec = 'strong_buy'
        elif final_score >= 70:
            base_rec = 'buy'
        elif final_score >= 50:
            base_rec = 'hold'
        else:
            base_rec = 'avoid'
        
        # Adjust for market conditions
        market_regime = market_context.get('regime', 'unknown')
        if market_regime == 'volatile' and base_rec in ['strong_buy', 'buy']:
            # Downgrade in volatile markets
            base_rec = 'hold' if base_rec == 'buy' else 'buy'
        
        return base_rec
    
    def _create_fallback_signal(self, signal: MultiTimeframeSignal) -> AIEnhancedSignal:
        """Create fallback enhanced signal on error"""
        return AIEnhancedSignal(
            original_signal=signal,
            ai_quality_score=signal.overall_strength * 100,
            ai_confidence=50.0,
            market_context_score=50.0,
            risk_assessment={
                'position_size': 1.0,
                'stop_loss': 0.02,
                'take_profit': 0.04,
                'max_risk': 0.01
            },
            final_score=signal.overall_strength * 100,
            recommendation='hold',
            market_regime='unknown',
            sector_strength=0.0,
            news_sentiment=0.0,
            suggested_position_size=1.0,
            suggested_stop_loss=0.02,
            suggested_take_profit=0.04,
            max_risk_per_trade=0.01
        )
    
    async def train_models(self, historical_data: pd.DataFrame):
        """Train AI models on historical data"""
        try:
            logger.info("Training AI models on historical data...")
            
            # Train signal quality predictor
            await self.quality_predictor.train(historical_data)
            
            # Train market context analyzer
            await self.market_analyzer.train(historical_data)
            
            # Update parameter optimizer
            await self.parameter_optimizer.update_parameters(historical_data)
            
            self.models_trained = True
            self.last_training_date = datetime.now()
            
            logger.info("AI models training completed successfully")
            
        except Exception as e:
            logger.error(f"Error training AI models: {e}")
            raise
    
    def get_model_status(self) -> Dict:
        """Get status of AI models"""
        return {
            'models_trained': self.models_trained,
            'last_training_date': self.last_training_date,
            'quality_predictor_ready': self.quality_predictor.is_trained(),
            'market_analyzer_ready': self.market_analyzer.is_ready(),
            'parameter_optimizer_ready': self.parameter_optimizer.is_ready(),
            'risk_engine_ready': self.risk_engine.is_ready()
        }
