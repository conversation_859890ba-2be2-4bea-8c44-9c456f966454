"""
Unified Data Manager for TTM Squeeze Trading System
"""
import pandas as pd
from typing import Optional, Dict, List, Union
import logging
from datetime import datetime, timedelta
import asyncio
from concurrent.futures import ThreadPoolExecutor

from .alpaca_provider import AlpacaDataProvider
from .fmp_provider import FMPDataProvider
from config import Config

logger = logging.getLogger(__name__)

class DataManager:
    """Unified data manager combining multiple data sources"""
    
    def __init__(self):
        """Initialize data manager with multiple providers"""
        self.alpaca_provider = None
        self.fmp_provider = None
        
        # Initialize providers
        try:
            self.alpaca_provider = AlpacaDataProvider()
            logger.info("Alpaca provider initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize Alpaca provider: {e}")
        
        try:
            self.fmp_provider = FMPDataProvider()
            logger.info("FMP provider initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize FMP provider: {e}")
        
        if not self.alpaca_provider and not self.fmp_provider:
            raise Exception("No data providers available")
        
        # Cache for frequently accessed data
        self.cache = {}
        self.cache_expiry = {}
        self.cache_duration = timedelta(minutes=5)
    
    def get_historical_data(self, symbol: str, timeframe: str, 
                          periods: int = 100, source: str = 'auto') -> Optional[pd.DataFrame]:
        """
        Get historical OHLCV data with fallback between providers
        
        Args:
            symbol: Stock symbol
            timeframe: Timeframe (1Min, 5Min, 15Min, 30Min, 1Hour, 1Day)
            periods: Number of periods
            source: Data source ('alpaca', 'fmp', 'auto')
            
        Returns:
            DataFrame with OHLCV data
        """
        cache_key = f"{symbol}_{timeframe}_{periods}"
        
        # Check cache first
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]
        
        data = None
        
        if source == 'auto':
            # Try Alpaca first for intraday data, FMP for daily
            if timeframe in ['1Min', '5Min', '15Min', '30Min', '1Hour'] and self.alpaca_provider:
                data = self.alpaca_provider.get_historical_data(symbol, timeframe, periods)
            
            # Fallback to FMP if Alpaca fails or for daily data
            if data is None and self.fmp_provider:
                data = self.fmp_provider.get_historical_data(symbol, timeframe, periods)
        
        elif source == 'alpaca' and self.alpaca_provider:
            data = self.alpaca_provider.get_historical_data(symbol, timeframe, periods)
        
        elif source == 'fmp' and self.fmp_provider:
            data = self.fmp_provider.get_historical_data(symbol, timeframe, periods)
        
        # Cache the result
        if data is not None:
            self.cache[cache_key] = data
            self.cache_expiry[cache_key] = datetime.now() + self.cache_duration
        
        return data
    
    def get_real_time_data(self, symbol: str, source: str = 'auto') -> Optional[Dict]:
        """Get real-time quote data"""
        
        if source == 'auto':
            # Try Alpaca first
            if self.alpaca_provider:
                data = self.alpaca_provider.get_real_time_data(symbol)
                if data:
                    return data
            
            # Fallback to FMP
            if self.fmp_provider:
                return self.fmp_provider.get_real_time_quote(symbol)
        
        elif source == 'alpaca' and self.alpaca_provider:
            return self.alpaca_provider.get_real_time_data(symbol)
        
        elif source == 'fmp' and self.fmp_provider:
            return self.fmp_provider.get_real_time_quote(symbol)
        
        return None
    
    def get_multiple_symbols_data(self, symbols: List[str], timeframe: str, 
                                periods: int = 100) -> Dict[str, pd.DataFrame]:
        """Get data for multiple symbols efficiently"""
        
        # Use ThreadPoolExecutor for parallel data fetching
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = {
                executor.submit(self.get_historical_data, symbol, timeframe, periods): symbol
                for symbol in symbols
            }
            
            results = {}
            for future in futures:
                symbol = futures[future]
                try:
                    data = future.result(timeout=30)
                    if data is not None:
                        results[symbol] = data
                except Exception as e:
                    logger.error(f"Error getting data for {symbol}: {e}")
        
        return results
    
    def get_stock_universe(self) -> List[str]:
        """Get the stock universe based on configuration"""
        universe = set()
        
        # Get S&P 500 stocks if configured
        if Config.STOCK_UNIVERSE['sp500'] and self.fmp_provider:
            try:
                sp500_symbols = self.fmp_provider.get_sp500_list()
                universe.update(sp500_symbols)
                logger.info(f"Added {len(sp500_symbols)} S&P 500 stocks to universe")
            except Exception as e:
                logger.error(f"Error getting S&P 500 list: {e}")
        
        # Filter by market cap if configured
        min_market_cap = Config.STOCK_UNIVERSE.get('min_market_cap')
        if min_market_cap and self.fmp_provider:
            try:
                large_cap_symbols = self.fmp_provider.get_market_cap_filter(min_market_cap)
                if universe:
                    # Intersect with existing universe
                    universe = universe.intersection(set(large_cap_symbols))
                else:
                    universe.update(large_cap_symbols)
                logger.info(f"Filtered to {len(universe)} stocks with market cap >= ${min_market_cap:,.0f}")
            except Exception as e:
                logger.error(f"Error filtering by market cap: {e}")
        
        # Convert to sorted list
        return sorted(list(universe))
    
    def is_market_open(self) -> bool:
        """Check if market is open"""
        if self.alpaca_provider:
            return self.alpaca_provider.is_market_open()
        
        # Fallback: simple time-based check
        now = datetime.now()
        if now.weekday() >= 5:  # Weekend
            return False
        
        # Market hours: 9:30 AM - 4:00 PM ET (simplified)
        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
        
        return market_open <= now <= market_close
    
    def get_company_info(self, symbol: str) -> Optional[Dict]:
        """Get company information"""
        if self.fmp_provider:
            return self.fmp_provider.get_company_profile(symbol)
        return None
    
    def validate_symbol(self, symbol: str) -> bool:
        """Validate if symbol exists and has data"""
        try:
            data = self.get_historical_data(symbol, '1Day', periods=5)
            return data is not None and len(data) > 0
        except Exception:
            return False
    
    def get_sector_performance(self) -> Optional[Dict]:
        """Get sector performance data"""
        if self.fmp_provider:
            return self.fmp_provider.get_sector_performance()
        return None
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid"""
        if cache_key not in self.cache:
            return False
        
        if cache_key not in self.cache_expiry:
            return False
        
        return datetime.now() < self.cache_expiry[cache_key]
    
    def clear_cache(self):
        """Clear all cached data"""
        self.cache.clear()
        self.cache_expiry.clear()
        logger.info("Data cache cleared")
    
    def get_cache_stats(self) -> Dict:
        """Get cache statistics"""
        valid_entries = sum(1 for key in self.cache.keys() if self._is_cache_valid(key))
        
        return {
            'total_entries': len(self.cache),
            'valid_entries': valid_entries,
            'expired_entries': len(self.cache) - valid_entries
        }
