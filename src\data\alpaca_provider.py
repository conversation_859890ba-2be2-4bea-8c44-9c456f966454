"""
Alpaca API Data Provider for TTM Squeeze Trading System
"""
import pandas as pd
import numpy as np
from typing import Optional, Dict, List
import logging
from datetime import datetime, timedelta
try:
    import alpaca_trade_api as tradeapi
    from alpaca_trade_api.rest import TimeFrame, TimeFrameUnit
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("alpaca_trade_api not installed. Install with: pip install alpaca-trade-api")
    tradeapi = None

from config import Config

logger = logging.getLogger(__name__)

class AlpacaDataProvider:
    """Alpaca API data provider for market data"""
    
    def __init__(self):
        """Initialize Alpaca API connection"""
        try:
            self.api = tradeapi.REST(
                Config.ALPACA_API_KEY,
                Config.ALPACA_SECRET_KEY,
                Config.ALPACA_BASE_URL,
                api_version='v2'
            )
            
            # Test connection
            account = self.api.get_account()
            logger.info(f"Connected to Alpaca API - Account: {account.id}")
            
            # Timeframe mapping
            self.timeframe_mapping = {
                '1Min': TimeFrame(1, TimeFrameUnit.Minute),
                '5Min': TimeFrame(5, TimeFrameUnit.Minute),
                '15Min': TimeFrame(15, TimeFrameUnit.Minute),
                '30Min': TimeFrame(30, TimeFrameUnit.Minute),
                '1Hour': TimeFrame(1, TimeFrameUnit.Hour),
                '1Day': TimeFrame(1, TimeFrameUnit.Day)
            }
            
        except Exception as e:
            logger.error(f"Failed to initialize Alpaca API: {e}")
            raise
    
    def get_historical_data(self, symbol: str, timeframe: str, 
                          periods: int = 100, end_date: Optional[datetime] = None) -> Optional[pd.DataFrame]:
        """
        Get historical OHLCV data for a symbol
        
        Args:
            symbol: Stock symbol
            timeframe: Timeframe (1Min, 5Min, 15Min, 30Min, 1Hour, 1Day)
            periods: Number of periods to retrieve
            end_date: End date for data (default: now)
            
        Returns:
            DataFrame with OHLCV data or None if error
        """
        try:
            if timeframe not in self.timeframe_mapping:
                logger.error(f"Unsupported timeframe: {timeframe}")
                return None
            
            # Calculate start date based on periods and timeframe
            if end_date is None:
                end_date = datetime.now()
            
            start_date = self._calculate_start_date(end_date, timeframe, periods)
            
            # Get bars from Alpaca
            bars = self.api.get_bars(
                symbol,
                self.timeframe_mapping[timeframe],
                start=start_date.isoformat(),
                end=end_date.isoformat(),
                adjustment='raw'
            )
            
            if not bars:
                logger.warning(f"No data returned for {symbol} {timeframe}")
                return None
            
            # Convert to DataFrame
            data = []
            for bar in bars:
                data.append({
                    'timestamp': bar.t,
                    'open': float(bar.o),
                    'high': float(bar.h),
                    'low': float(bar.l),
                    'close': float(bar.c),
                    'volume': int(bar.v)
                })
            
            df = pd.DataFrame(data)
            df.set_index('timestamp', inplace=True)
            df.index = pd.to_datetime(df.index)
            
            # Ensure we have the requested number of periods
            if len(df) < periods * 0.8:  # Allow 20% tolerance
                logger.warning(f"Insufficient data for {symbol}: got {len(df)}, requested {periods}")
            
            return df.tail(periods)
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return None
    
    def get_real_time_data(self, symbol: str) -> Optional[Dict]:
        """
        Get real-time quote data for a symbol
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with current quote data
        """
        try:
            quote = self.api.get_latest_quote(symbol)
            
            if quote is None:
                return None
            
            return {
                'symbol': symbol,
                'timestamp': quote.t,
                'bid': float(quote.bp),
                'ask': float(quote.ap),
                'bid_size': int(quote.bs),
                'ask_size': int(quote.as_),
                'last_price': (float(quote.bp) + float(quote.ap)) / 2  # Mid price
            }
            
        except Exception as e:
            logger.error(f"Error getting real-time data for {symbol}: {e}")
            return None
    
    def get_latest_bar(self, symbol: str, timeframe: str) -> Optional[Dict]:
        """
        Get the latest bar for a symbol
        
        Args:
            symbol: Stock symbol
            timeframe: Timeframe
            
        Returns:
            Dictionary with latest bar data
        """
        try:
            if timeframe not in self.timeframe_mapping:
                return None
            
            bars = self.api.get_bars(
                symbol,
                self.timeframe_mapping[timeframe],
                start=(datetime.now() - timedelta(days=1)).isoformat(),
                end=datetime.now().isoformat(),
                adjustment='raw'
            )
            
            if not bars:
                return None
            
            latest_bar = bars[-1]
            
            return {
                'symbol': symbol,
                'timestamp': latest_bar.t,
                'open': float(latest_bar.o),
                'high': float(latest_bar.h),
                'low': float(latest_bar.l),
                'close': float(latest_bar.c),
                'volume': int(latest_bar.v)
            }
            
        except Exception as e:
            logger.error(f"Error getting latest bar for {symbol}: {e}")
            return None
    
    def get_multiple_symbols_data(self, symbols: List[str], timeframe: str, 
                                periods: int = 100) -> Dict[str, pd.DataFrame]:
        """
        Get historical data for multiple symbols
        
        Args:
            symbols: List of stock symbols
            timeframe: Timeframe
            periods: Number of periods
            
        Returns:
            Dictionary with symbol as key and DataFrame as value
        """
        data_dict = {}
        
        for symbol in symbols:
            data = self.get_historical_data(symbol, timeframe, periods)
            if data is not None:
                data_dict[symbol] = data
        
        return data_dict
    
    def _calculate_start_date(self, end_date: datetime, timeframe: str, periods: int) -> datetime:
        """Calculate start date based on timeframe and periods"""
        
        if timeframe == '1Min':
            delta = timedelta(minutes=periods * 2)  # Buffer for market hours
        elif timeframe == '5Min':
            delta = timedelta(minutes=periods * 10)
        elif timeframe == '15Min':
            delta = timedelta(minutes=periods * 30)
        elif timeframe == '30Min':
            delta = timedelta(hours=periods)
        elif timeframe == '1Hour':
            delta = timedelta(hours=periods * 2)
        elif timeframe == '1Day':
            delta = timedelta(days=periods * 2)
        else:
            delta = timedelta(days=periods)
        
        return end_date - delta
    
    def is_market_open(self) -> bool:
        """Check if market is currently open"""
        try:
            clock = self.api.get_clock()
            return clock.is_open
        except Exception as e:
            logger.error(f"Error checking market status: {e}")
            return False
    
    def get_market_calendar(self, start_date: datetime, end_date: datetime) -> List[Dict]:
        """Get market calendar for date range"""
        try:
            calendar = self.api.get_calendar(
                start=start_date.strftime('%Y-%m-%d'),
                end=end_date.strftime('%Y-%m-%d')
            )
            
            return [
                {
                    'date': day.date,
                    'open': day.open,
                    'close': day.close
                }
                for day in calendar
            ]
            
        except Exception as e:
            logger.error(f"Error getting market calendar: {e}")
            return []
